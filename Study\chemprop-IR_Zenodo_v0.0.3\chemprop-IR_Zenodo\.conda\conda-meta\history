==> 2025-08-25 21:49:56 <==
# cmd: D:\Users\sqchenb\AppData\Local\anaconda3\Lib\site-packages\conda\__main__.py create --yes --prefix .conda python=3.12
# conda version: 24.9.2
+defaults/noarch::pip-25.1-pyhc872135_2
+defaults/noarch::tzdata-2025b-h04d1e81_0
+defaults/win-64::bzip2-1.0.8-h2bbff1b_6
+defaults/win-64::ca-certificates-2025.7.15-haa95532_0
+defaults/win-64::expat-2.7.1-h8ddb27b_0
+defaults/win-64::libffi-3.4.4-hd77b12b_1
+defaults/win-64::openssl-3.0.17-h35632f6_0
+defaults/win-64::python-3.12.11-h716150d_0
+defaults/win-64::setuptools-78.1.1-py312haa95532_0
+defaults/win-64::sqlite-3.50.2-hda9a48d_1
+defaults/win-64::tk-8.6.15-hf199647_0
+defaults/win-64::ucrt-10.0.22621.0-haa95532_0
+defaults/win-64::vc-14.3-h2df5915_10
+defaults/win-64::vc14_runtime-14.44.35208-h4927774_10
+defaults/win-64::vs2015_runtime-14.44.35208-ha6b5a95_10
+defaults/win-64::wheel-0.45.1-py312haa95532_0
+defaults/win-64::xz-5.6.4-h4754444_1
+defaults/win-64::zlib-1.2.13-h8cc25b3_1
# update specs: ['python=3.12']
