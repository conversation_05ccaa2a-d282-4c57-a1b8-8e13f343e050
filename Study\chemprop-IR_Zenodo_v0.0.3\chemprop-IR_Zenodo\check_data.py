#!/usr/bin/env python3

import pandas as pd
import numpy as np

def check_raw_data():
    """检查原始CSV数据中的特殊值"""
    
    # 读取原始CSV文件
    df = pd.read_csv('trained_ir_model/experiment_model/preds_smoke.csv')
    
    # 找到特定分子
    target_smiles = 'CC(C)(C)C1=CC(=O)C=C(C(C)(C)C)C1=O'
    row = df[df['smiles'] == target_smiles]
    
    if not row.empty:
        # 获取频率列（除了smiles列）
        freq_cols = [col for col in df.columns if col != 'smiles']
        
        # 获取原始数据（不转换类型）
        raw_data = row.iloc[0][freq_cols]
        
        print(f'分子: {target_smiles}')
        print(f'总列数: {len(freq_cols)}')
        
        # 检查数据类型和特殊值
        empty_count = 0
        nan_str_count = 0
        actual_nan_count = 0
        
        for i, val in enumerate(raw_data):
            if pd.isna(val):
                actual_nan_count += 1
            elif str(val).strip() == '':
                empty_count += 1
            elif str(val).lower() == 'nan':
                nan_str_count += 1
        
        print(f'空字符串数量: {empty_count}')
        print(f'字符串"nan"数量: {nan_str_count}')
        print(f'实际NaN数量: {actual_nan_count}')
        
        # 显示一些原始值
        print(f'前10个原始值: {list(raw_data[:10])}')
        print(f'后10个原始值: {list(raw_data[-10:])}')
        
        # 检查中间部分是否有特殊值
        middle_start = len(raw_data) // 2 - 5
        middle_end = len(raw_data) // 2 + 5
        print(f'中间部分原始值 ({middle_start}-{middle_end}): {list(raw_data[middle_start:middle_end])}')
        
        # 查找所有非数值的位置
        non_numeric_indices = []
        for i, val in enumerate(raw_data):
            try:
                float(val)
            except (ValueError, TypeError):
                non_numeric_indices.append(i)
        
        if non_numeric_indices:
            print(f'非数值位置: {non_numeric_indices[:10]}...' if len(non_numeric_indices) > 10 else f'非数值位置: {non_numeric_indices}')
            for idx in non_numeric_indices[:5]:
                print(f'  位置 {idx}: 值 = "{raw_data.iloc[idx]}" (类型: {type(raw_data.iloc[idx])})')
        else:
            print('所有值都是数值类型')
            
        # 模拟原始脚本的处理逻辑
        print('\n模拟原始脚本处理:')
        y_vals = []
        for col in freq_cols:
            val = row.iloc[0][col]
            try:
                y_vals.append(float(val) if val != '' else float('nan'))
            except ValueError:
                y_vals.append(float('nan'))
        
        y_vals = np.array(y_vals)
        nan_count_processed = np.isnan(y_vals).sum()
        print(f'处理后的NaN数量: {nan_count_processed}')
        
        if nan_count_processed > 0:
            nan_indices = np.where(np.isnan(y_vals))[0]
            print(f'NaN位置: {nan_indices[:20]}...' if len(nan_indices) > 20 else f'NaN位置: {nan_indices}')
    else:
        print('未找到该分子')

if __name__ == '__main__':
    check_raw_data()
