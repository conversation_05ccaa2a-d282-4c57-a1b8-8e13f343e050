#!/usr/bin/env python3

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

def analyze_ensemble_prediction():
    """分析集成预测的详细过程"""
    
    print("=== ChemProp-IR 集成预测分析 ===\n")
    
    # 1. 检查模型文件
    model_dir = Path("trained_ir_model/experiment_model/model_files")
    model_files = list(model_dir.glob("*.pt"))
    model_files.sort()
    
    print(f"1. 模型集成信息:")
    print(f"   模型文件数量: {len(model_files)}")
    print(f"   模型文件列表:")
    for i, model_file in enumerate(model_files, 1):
        print(f"     {i:2d}. {model_file.name}")
    
    # 2. 分析预测数据
    df = pd.read_csv('trained_ir_model/experiment_model/preds_smoke.csv')
    
    print(f"\n2. 预测数据分析:")
    print(f"   总行数: {len(df)}")
    print(f"   总列数: {len(df.columns)}")
    print(f"   SMILES列: 1")
    print(f"   频率列数: {len(df.columns) - 1}")
    
    # 3. 分析重复分子
    smiles_counts = df['smiles'].value_counts()
    duplicated_smiles = smiles_counts[smiles_counts > 1]
    
    print(f"\n3. 重复分子分析:")
    print(f"   唯一分子数: {len(smiles_counts)}")
    print(f"   重复分子数: {len(duplicated_smiles)}")
    
    if len(duplicated_smiles) > 0:
        print(f"   重复分子详情:")
        for smiles, count in duplicated_smiles.items():
            print(f"     {smiles}: 出现 {count} 次")
            
            # 找到这个分子的所有行
            rows = df[df['smiles'] == smiles]
            freq_cols = [col for col in df.columns if col != 'smiles']
            
            print(f"       行索引: {list(rows.index)}")
            
            # 分析每行的数据质量
            for idx, (row_idx, row) in enumerate(rows.iterrows()):
                values = pd.to_numeric(row[freq_cols], errors='coerce').values
                nan_count = np.isnan(values).sum()
                valid_count = len(values) - nan_count
                
                if nan_count > 0:
                    nan_indices = np.where(np.isnan(values))[0]
                    freq_start = 400 + nan_indices[0] * 2
                    freq_end = 400 + nan_indices[-1] * 2
                    print(f"       第{row_idx}行: {valid_count}/{len(values)} 有效, NaN区域: {freq_start}-{freq_end} cm^-1")
                else:
                    print(f"       第{row_idx}行: {valid_count}/{len(values)} 有效 (完整)")
    
    # 4. 分析预测过程
    print(f"\n4. 预测过程分析:")
    print(f"   根据代码分析，预测过程如下:")
    print(f"   a) 加载 {len(model_files)} 个训练好的模型")
    print(f"   b) 每个模型独立对输入分子进行预测")
    print(f"   c) 将所有模型的预测结果进行平均")
    print(f"   d) 输出最终的集成预测结果")
    
    # 5. 解释为什么有重复行
    print(f"\n5. 重复行的原因:")
    print(f"   根据chemprop-IR的实现，可能的原因包括:")
    print(f"   a) 集成预测过程中，某些模型预测失败，产生NaN值")
    print(f"   b) 不同的特征组合或预测条件")
    print(f"   c) 模型在某些频率区域的预测不稳定")
    print(f"   d) 数据后处理过程中的问题")
    
    # 6. 检查spectral_mask
    mask_file = Path("trained_ir_model/experiment_model/spectral_mask.csv")
    if mask_file.exists():
        print(f"\n6. Spectral Mask 分析:")
        mask_df = pd.read_csv(mask_file)
        print(f"   Mask文件存在: {mask_file}")
        print(f"   Mask维度: {mask_df.shape}")
        print(f"   这可能影响某些频率区域的预测")
    else:
        print(f"\n6. Spectral Mask: 未找到mask文件")
    
    # 7. 建议
    print(f"\n7. 建议:")
    print(f"   a) 使用完整数据的行（如第20行和第22行）")
    print(f"   b) 对缺失数据进行插值修复")
    print(f"   c) 检查模型训练过程中的spectral mask设置")
    print(f"   d) 重新运行预测，可能得到不同的结果")
    
    # 8. 绘制数据质量分布图
    print(f"\n8. 生成数据质量分析图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 8.1 每行的NaN数量分布
    freq_cols = [col for col in df.columns if col != 'smiles']
    nan_counts = []
    for _, row in df.iterrows():
        values = pd.to_numeric(row[freq_cols], errors='coerce').values
        nan_counts.append(np.isnan(values).sum())
    
    axes[0, 0].hist(nan_counts, bins=20, alpha=0.7, color='red')
    axes[0, 0].set_title('每行NaN值数量分布')
    axes[0, 0].set_xlabel('NaN数量')
    axes[0, 0].set_ylabel('行数')
    
    # 8.2 分子重复次数分布
    repeat_counts = smiles_counts.values
    axes[0, 1].hist(repeat_counts, bins=range(1, max(repeat_counts)+2), alpha=0.7, color='blue')
    axes[0, 1].set_title('分子重复次数分布')
    axes[0, 1].set_xlabel('重复次数')
    axes[0, 1].set_ylabel('分子数量')
    
    # 8.3 数据完整性按行索引
    axes[1, 0].plot(range(len(nan_counts)), nan_counts, 'o-', markersize=4)
    axes[1, 0].set_title('数据完整性按行索引')
    axes[1, 0].set_xlabel('行索引')
    axes[1, 0].set_ylabel('NaN数量')
    
    # 8.4 模型数量信息
    axes[1, 1].bar(['训练模型数', '预测行数', '唯一分子数'], 
                   [len(model_files), len(df), len(smiles_counts)],
                   color=['green', 'orange', 'purple'])
    axes[1, 1].set_title('数据统计概览')
    axes[1, 1].set_ylabel('数量')
    
    plt.tight_layout()
    plt.savefig('ensemble_analysis.png', dpi=150, bbox_inches='tight')
    print(f"   分析图已保存为: ensemble_analysis.png")
    
    plt.close()

if __name__ == '__main__':
    analyze_ensemble_prediction()
