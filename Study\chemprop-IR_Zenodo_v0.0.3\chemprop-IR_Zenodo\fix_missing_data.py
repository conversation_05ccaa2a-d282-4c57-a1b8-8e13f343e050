#!/usr/bin/env python3

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import interpolate

def fix_missing_data():
    """修复缺失数据并重新绘制图像"""
    
    # 读取CSV文件
    df = pd.read_csv('trained_ir_model/experiment_model/preds_smoke.csv')
    
    target_smiles = 'CC(C)(C)C1=CC(=O)C=C(C(C)(C)C)C1=O'
    matching_rows = df[df['smiles'] == target_smiles]
    
    freq_cols = [col for col in df.columns if col != 'smiles']
    freqs = np.arange(400, 4002, 2)
    
    print(f'找到 {len(matching_rows)} 行匹配数据')
    
    # 获取所有行的数据
    all_data = []
    for idx, (row_idx, row) in enumerate(matching_rows.iterrows()):
        values = pd.to_numeric(row[freq_cols], errors='coerce').values
        all_data.append((row_idx, values))
        
        nan_count = np.isnan(values).sum()
        print(f'第{row_idx}行: NaN数量 = {nan_count}')
    
    # 找到有缺失数据的行（第21行）
    problematic_row_idx = 21
    problematic_data = all_data[1][1]  # 第21行的数据
    
    # 方法1：使用其他完整行的平均值
    complete_data = []
    for row_idx, values in all_data:
        if np.isnan(values).sum() == 0:  # 完整数据
            complete_data.append(values)
    
    if len(complete_data) >= 2:
        # 使用完整数据的平均值
        averaged_data = np.mean(complete_data, axis=0)
        print(f'使用 {len(complete_data)} 行完整数据的平均值')
        
        # 创建修复后的数据
        fixed_data = problematic_data.copy()
        nan_mask = np.isnan(fixed_data)
        fixed_data[nan_mask] = averaged_data[nan_mask]
        
        print(f'修复了 {np.sum(nan_mask)} 个缺失值')
        
    # 方法2：线性插值
    interpolated_data = problematic_data.copy()
    nan_mask = np.isnan(interpolated_data)
    
    if np.sum(~nan_mask) > 1:  # 至少有2个有效点才能插值
        valid_indices = np.where(~nan_mask)[0]
        valid_values = interpolated_data[valid_indices]
        
        # 创建插值函数
        f = interpolate.interp1d(valid_indices, valid_values, 
                               kind='linear', fill_value='extrapolate')
        
        # 对缺失位置进行插值
        missing_indices = np.where(nan_mask)[0]
        interpolated_data[missing_indices] = f(missing_indices)
        
        print(f'插值修复了 {len(missing_indices)} 个缺失值')
    
    # 绘制对比图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 原始有问题的数据
    axes[0, 0].plot(freqs, problematic_data, lw=1.2, color='red')
    axes[0, 0].set_title('原始数据（有缺失）')
    axes[0, 0].set_xlabel('Wavenumber (cm^-1)')
    axes[0, 0].set_ylabel('Intensity')
    axes[0, 0].invert_xaxis()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 使用平均值修复的数据
    if len(complete_data) >= 2:
        axes[0, 1].plot(freqs, fixed_data, lw=1.2, color='blue')
        axes[0, 1].set_title('使用平均值修复')
        axes[0, 1].set_xlabel('Wavenumber (cm^-1)')
        axes[0, 1].set_ylabel('Intensity')
        axes[0, 1].invert_xaxis()
        axes[0, 1].grid(True, alpha=0.3)
    
    # 使用插值修复的数据
    axes[1, 0].plot(freqs, interpolated_data, lw=1.2, color='green')
    axes[1, 0].set_title('使用插值修复')
    axes[1, 0].set_xlabel('Wavenumber (cm^-1)')
    axes[1, 0].set_ylabel('Intensity')
    axes[1, 0].invert_xaxis()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 完整数据作为参考（第20行）
    reference_data = all_data[0][1]  # 第20行
    axes[1, 1].plot(freqs, reference_data, lw=1.2, color='purple')
    axes[1, 1].set_title('参考数据（第20行）')
    axes[1, 1].set_xlabel('Wavenumber (cm^-1)')
    axes[1, 1].set_ylabel('Intensity')
    axes[1, 1].invert_xaxis()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('data_comparison.png', dpi=150, bbox_inches='tight')
    print('对比图已保存为: data_comparison.png')
    
    # 保存修复后的单独图像
    fig2, ax = plt.subplots(figsize=(12, 6))
    
    if len(complete_data) >= 2:
        ax.plot(freqs, fixed_data, lw=1.5, color='blue', label='平均值修复')
    ax.plot(freqs, interpolated_data, lw=1.5, color='green', label='插值修复', alpha=0.7)
    ax.plot(freqs, reference_data, lw=1.5, color='purple', label='参考数据（第20行）', alpha=0.7)
    
    ax.set_xlabel('Wavenumber (cm^-1)')
    ax.set_ylabel('Predicted Intensity')
    ax.set_title(f'修复后的IR光谱: {target_smiles}')
    ax.invert_xaxis()
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    plt.tight_layout()
    plt.savefig('fixed_spectrum.png', dpi=150, bbox_inches='tight')
    print('修复后的光谱图已保存为: fixed_spectrum.png')
    
    plt.close('all')
    
    # 显示缺失区域的详细信息
    nan_indices = np.where(np.isnan(problematic_data))[0]
    if len(nan_indices) > 0:
        freq_start = freqs[nan_indices[0]]
        freq_end = freqs[nan_indices[-1]]
        print(f'\n缺失数据详情:')
        print(f'缺失位置: {nan_indices[0]} - {nan_indices[-1]}')
        print(f'缺失频率范围: {freq_start} - {freq_end} cm^-1')
        print(f'缺失长度: {freq_end - freq_start} cm^-1')
        print(f'缺失数据点数: {len(nan_indices)}')

if __name__ == '__main__':
    fix_missing_data()
