#!/usr/bin/env python3

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def analyze_all_matching_rows():
    """分析所有匹配行的数据"""
    
    # 读取CSV文件
    df = pd.read_csv('trained_ir_model/experiment_model/preds_smoke.csv')
    
    target_smiles = 'CC(C)(C)C1=CC(=O)C=C(C(C)(C)C)C1=O'
    
    # 找到所有匹配的行
    matching_rows = df[df['smiles'] == target_smiles]
    print(f'找到 {len(matching_rows)} 行匹配数据')
    
    freq_cols = [col for col in df.columns if col != 'smiles']
    freqs = np.arange(400, 4002, 2)
    
    print(f'频率列数: {len(freq_cols)}')
    print(f'频率范围: {freqs[0]} - {freqs[-1]} cm^-1')
    
    # 分析每一行
    for idx, (row_idx, row) in enumerate(matching_rows.iterrows()):
        print(f'\n=== 第{row_idx}行数据 (匹配{idx+1}) ===')
        
        # 获取原始数据（不转换类型）
        raw_values = row[freq_cols]
        
        # 检查原始数据类型
        print(f'原始数据类型样本: {[type(val) for val in raw_values[:5]]}')
        
        # 转换为数值
        values = pd.to_numeric(raw_values, errors='coerce').values
        
        # 统计信息
        nan_count = np.isnan(values).sum()
        valid_count = len(values) - nan_count
        
        print(f'总数据点: {len(values)}')
        print(f'NaN数量: {nan_count}')
        print(f'有效数据点: {valid_count}')
        
        if nan_count > 0:
            nan_indices = np.where(np.isnan(values))[0]
            print(f'NaN位置: {nan_indices[:20]}...' if len(nan_indices) > 20 else f'NaN位置: {nan_indices}')
            
            # 检查连续NaN区域
            if len(nan_indices) > 1:
                consecutive_groups = []
                start = nan_indices[0]
                for i in range(1, len(nan_indices)):
                    if nan_indices[i] != nan_indices[i-1] + 1:
                        consecutive_groups.append((start, nan_indices[i-1]))
                        start = nan_indices[i]
                consecutive_groups.append((start, nan_indices[-1]))
                
                print(f'连续NaN区域: {len(consecutive_groups)} 个')
                for i, (start, end) in enumerate(consecutive_groups[:5]):
                    # 计算对应的频率
                    freq_start = 400 + start * 2
                    freq_end = 400 + end * 2
                    print(f'  区域{i+1}: 位置{start}-{end} (频率{freq_start}-{freq_end} cm^-1, 长度{end-start+1})')
        
        # 有效数据统计
        if valid_count > 0:
            valid_values = values[~np.isnan(values)]
            print(f'有效数据范围: {np.min(valid_values):.6f} - {np.max(valid_values):.6f}')
            print(f'有效数据平均值: {np.mean(valid_values):.6f}')
        
        # 显示前后几个值
        print(f'前5个值: {values[:5]}')
        print(f'后5个值: {values[-5:]}')
        
        # 检查原始字符串值中是否有特殊内容
        print(f'前5个原始值: {list(raw_values[:5])}')
        print(f'后5个原始值: {list(raw_values[-5:])}')
        
        # 绘制这一行的数据
        fig, ax = plt.subplots(figsize=(12, 4))
        ax.plot(freqs, values, lw=1.2, label=f'Row {row_idx}')
        ax.set_xlabel("Wavenumber (cm^-1)")
        ax.set_ylabel("Predicted intensity")
        ax.invert_xaxis()
        ax.set_title(f'Row {row_idx}: {target_smiles}')
        ax.grid(True, ls=':', alpha=0.4)
        ax.legend()
        
        # 添加NaN区域标记
        if nan_count > 0:
            for start, end in consecutive_groups:
                freq_start = 400 + start * 2
                freq_end = 400 + end * 2
                ax.axvspan(freq_end, freq_start, alpha=0.3, color='red', label=f'NaN region')
        
        plt.tight_layout()
        
        output_file = f'row_{row_idx}_analysis.png'
        plt.savefig(output_file, dpi=150)
        print(f'图像已保存为: {output_file}')
        plt.close()

if __name__ == '__main__':
    analyze_all_matching_rows()
