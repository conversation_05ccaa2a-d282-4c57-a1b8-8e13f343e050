"""
Module containing RDKit functionality for working with molecular file formats.
"""
from __future__ import annotations
import rdkit.Chem
import typing
__all__ = ['AddMetadataToPNGFile', 'AddMetadataToPNGString', 'AtomFromSmarts', 'AtomFromSmiles', 'BondFromSmarts', 'BondFromSmiles', 'CXSmilesFields', 'CanonicalRankAtoms', 'CanonicalRankAtomsInFragment', 'CanonicalizeEnhancedStereo', 'CreateAtomBoolPropertyList', 'CreateAtomDoublePropertyList', 'CreateAtomIntPropertyList', 'CreateAtomStringPropertyList', 'ForwardSDMolSupplier', 'MaeMolSupplier', 'MaeWriter', 'MetadataFromPNGFile', 'MetadataFromPNGString', 'MolFragmentToCXSmarts', 'MolFragmentToCXSmiles', 'MolFragmentToSmarts', 'MolFragmentToSmiles', 'MolFromFASTA', 'MolFromHELM', 'MolFromMol2Block', 'MolFromMol2File', 'MolFromMolBlock', 'MolFromMolFile', 'MolFromMrvBlock', 'MolFromMrvFile', 'MolFromPDBBlock', 'MolFromPDBFile', 'MolFromPNGFile', 'MolFromPNGString', 'MolFromRDKitSVG', 'MolFromSCSRBlock', 'MolFromSCSRFile', 'MolFromSCSRParams', 'MolFromSequence', 'MolFromSmarts', 'MolFromSmiles', 'MolFromTPLBlock', 'MolFromTPLFile', 'MolFromXYZBlock', 'MolFromXYZFile', 'MolMetadataToPNGFile', 'MolMetadataToPNGString', 'MolToCMLBlock', 'MolToCMLFile', 'MolToCXSmarts', 'MolToCXSmiles', 'MolToFASTA', 'MolToHELM', 'MolToMolBlock', 'MolToMolFile', 'MolToMrvBlock', 'MolToMrvFile', 'MolToPDBBlock', 'MolToPDBFile', 'MolToRandomSmilesVect', 'MolToSequence', 'MolToSmarts', 'MolToSmiles', 'MolToTPLBlock', 'MolToTPLFile', 'MolToV2KMolBlock', 'MolToV3KMolBlock', 'MolToV3KMolFile', 'MolToXYZBlock', 'MolToXYZFile', 'MolWriterParams', 'MolsFromCDXML', 'MolsFromCDXMLFile', 'MolsFromPNGFile', 'MolsFromPNGString', 'MultithreadedSDMolSupplier', 'MultithreadedSmilesMolSupplier', 'PDBWriter', 'PNGMetadataParams', 'RestoreBondDirOption', 'SCSRBaseHbondOptions', 'SCSRTemplateNames', 'SDMolSupplier', 'SDWriter', 'SmartsParserParams', 'SmilesMolSupplier', 'SmilesMolSupplierFromText', 'SmilesParserParams', 'SmilesWriteParams', 'SmilesWriter', 'TDTMolSupplier', 'TDTWriter']
class CXSmilesFields(Boost.Python.enum):
    CX_ALL: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ALL
    CX_ALL_BUT_COORDS: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ALL_BUT_COORDS
    CX_ATOM_LABELS: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ATOM_LABELS
    CX_ATOM_PROPS: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ATOM_PROPS
    CX_BOND_ATROPISOMER: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_BOND_ATROPISOMER
    CX_BOND_CFG: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_BOND_CFG
    CX_COORDINATE_BONDS: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_COORDINATE_BONDS
    CX_COORDS: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_COORDS
    CX_ENHANCEDSTEREO: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ENHANCEDSTEREO
    CX_LINKNODES: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_LINKNODES
    CX_MOLFILE_VALUES: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_MOLFILE_VALUES
    CX_NONE: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_NONE
    CX_POLYMER: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_POLYMER
    CX_RADICALS: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_RADICALS
    CX_SGROUPS: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_SGROUPS
    CX_ZERO_BONDS: typing.ClassVar[CXSmilesFields]  # value = rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ZERO_BONDS
    __slots__: typing.ClassVar[tuple] = tuple()
    names: typing.ClassVar[dict]  # value = {'CX_NONE': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_NONE, 'CX_ATOM_LABELS': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ATOM_LABELS, 'CX_MOLFILE_VALUES': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_MOLFILE_VALUES, 'CX_COORDS': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_COORDS, 'CX_RADICALS': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_RADICALS, 'CX_ATOM_PROPS': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ATOM_PROPS, 'CX_LINKNODES': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_LINKNODES, 'CX_ENHANCEDSTEREO': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ENHANCEDSTEREO, 'CX_SGROUPS': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_SGROUPS, 'CX_POLYMER': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_POLYMER, 'CX_BOND_CFG': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_BOND_CFG, 'CX_BOND_ATROPISOMER': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_BOND_ATROPISOMER, 'CX_COORDINATE_BONDS': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_COORDINATE_BONDS, 'CX_ZERO_BONDS': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ZERO_BONDS, 'CX_ALL': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ALL, 'CX_ALL_BUT_COORDS': rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ALL_BUT_COORDS}
    values: typing.ClassVar[dict]  # value = {0: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_NONE, 1: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ATOM_LABELS, 2: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_MOLFILE_VALUES, 4: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_COORDS, 8: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_RADICALS, 16: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ATOM_PROPS, 32: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_LINKNODES, 64: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ENHANCEDSTEREO, 128: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_SGROUPS, 256: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_POLYMER, 512: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_BOND_CFG, 1024: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_BOND_ATROPISOMER, 2048: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_COORDINATE_BONDS, 8192: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ZERO_BONDS, 2147483647: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ALL, 2147483643: rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ALL_BUT_COORDS}
class ForwardSDMolSupplier(Boost.Python.instance):
    """
    A class which supplies molecules from file-like object containing SD data.
    
      Usage examples:
    
        1) Lazy evaluation: the molecules are not constructed until we ask for them:
    
           >>> suppl = ForwardSDMolSupplier(file('in.sdf'))
           >>> for mol in suppl:
           ...    if mol is not None: mol.GetNumAtoms()
    
        2) we can also read from compressed files: 
    
           >>> import gzip
           >>> suppl = ForwardSDMolSupplier(gzip.open('in.sdf.gz'))
           >>> for mol in suppl:
           ...   if mol is not None: print mol.GetNumAtoms()
    
      Properties in the SD file are used to set properties on each molecule.
      The properties are accessible using the mol.GetProp(propName) method.
    
    """
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def GetEOFHitOnRead(self) -> bool:
        """
            Returns whether or EOF was hit while parsing the previous entry.
            
        
            C++ signature :
                bool GetEOFHitOnRead(class `anonymous namespace'::LocalForwardSDMolSupplier {lvalue})
        """
    def GetProcessPropertyLists(self) -> bool:
        """
            returns whether or not any property lists that are present will be processed when reading molecules
        
            C++ signature :
                bool GetProcessPropertyLists(class `anonymous namespace'::LocalForwardSDMolSupplier {lvalue})
        """
    def SetProcessPropertyLists(self, val: bool) -> None:
        """
            sets whether or not any property lists that are present will be processed when reading molecules
        
            C++ signature :
                void SetProcessPropertyLists(class `anonymous namespace'::LocalForwardSDMolSupplier {lvalue},bool)
        """
    def __enter__(self) -> ForwardSDMolSupplier:
        """
            C++ signature :
                class `anonymous namespace'::LocalForwardSDMolSupplier * __ptr64 __enter__(class `anonymous namespace'::LocalForwardSDMolSupplier * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class `anonymous namespace'::LocalForwardSDMolSupplier * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    @typing.overload
    def __init__(self, fileobj: typing.Any, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class boost::python::api::object {lvalue} [,bool=True [,bool=True [,bool=True]]])
        """
    @typing.overload
    def __init__(self, streambuf: streambuf, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class boost_adaptbx::python::streambuf {lvalue} [,bool=True [,bool=True [,bool=True]]])
        """
    @typing.overload
    def __init__(self, filename: str, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,bool=True]]])
        """
    def __iter__(self) -> ForwardSDMolSupplier:
        """
            C++ signature :
                class `anonymous namespace'::LocalForwardSDMolSupplier * __ptr64 __iter__(class `anonymous namespace'::LocalForwardSDMolSupplier * __ptr64)
        """
    def __next__(self) -> rdkit.Chem.Mol:
        """
            Returns the next molecule in the file.  Raises _StopIteration_ on EOF.
            
        
            C++ signature :
                class RDKit::ROMol * __ptr64 __next__(class `anonymous namespace'::LocalForwardSDMolSupplier * __ptr64)
        """
    def atEnd(self) -> bool:
        """
            Returns whether or not we have hit EOF.
            
        
            C++ signature :
                bool atEnd(class `anonymous namespace'::LocalForwardSDMolSupplier {lvalue})
        """
class MaeMolSupplier(Boost.Python.instance):
    """
    A class which supplies molecules from file-like object containing Maestro data.
    
      Usage examples:
    
        1) Lazy evaluation: the molecules are not constructed until we ask for them:
    
           >>> suppl = MaeMolSupplier(file('in.mae'))
           >>> for mol in suppl:
           ...    if mol is not None: mol.GetNumAtoms()
    
        2) we can also read from compressed files: 
    
           >>> import gzip
           >>> suppl = MaeMolSupplier(gzip.open('in.maegz'))
           >>> for mol in suppl:
           ...   if mol is not None: print mol.GetNumAtoms()
    
      Properties in the Maestro file are used to set properties on each molecule.
      The properties are accessible using the mol.GetProp(propName) method.
    
    """
    __instance_size__: typing.ClassVar[int] = 48
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def SetData(self, data: str, sanitize: bool = True, removeHs: bool = True) -> None:
        """
            Sets the text to be parsed
        
            C++ signature :
                void SetData(class `anonymous namespace'::LocalMaeMolSupplier {lvalue},class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True]])
        """
    def __enter__(self) -> MaeMolSupplier:
        """
            C++ signature :
                class `anonymous namespace'::LocalMaeMolSupplier * __ptr64 __enter__(class `anonymous namespace'::LocalMaeMolSupplier * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class `anonymous namespace'::LocalMaeMolSupplier * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    def __getitem__(self, idx: int) -> rdkit.Chem.Mol:
        """
            C++ signature :
                class RDKit::ROMol * __ptr64 __getitem__(class `anonymous namespace'::LocalMaeMolSupplier * __ptr64,int)
        """
    @typing.overload
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @typing.overload
    def __init__(self, fileobj: typing.Any, sanitize: bool = True, removeHs: bool = True) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class boost::python::api::object {lvalue} [,bool=True [,bool=True]])
        """
    @typing.overload
    def __init__(self, streambuf: streambuf, sanitize: bool = True, removeHs: bool = True) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class boost_adaptbx::python::streambuf {lvalue} [,bool=True [,bool=True]])
        """
    @typing.overload
    def __init__(self, filename: str, sanitize: bool = True, removeHs: bool = True) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True]])
        """
    def __iter__(self) -> MaeMolSupplier:
        """
            C++ signature :
                class `anonymous namespace'::LocalMaeMolSupplier * __ptr64 __iter__(class `anonymous namespace'::LocalMaeMolSupplier * __ptr64)
        """
    def __len__(self) -> int:
        """
            C++ signature :
                unsigned int __len__(class `anonymous namespace'::LocalMaeMolSupplier {lvalue})
        """
    def __next__(self) -> rdkit.Chem.Mol:
        """
            Returns the next molecule in the file.  Raises _StopIteration_ on EOF.
            
        
            C++ signature :
                class RDKit::ROMol * __ptr64 __next__(class `anonymous namespace'::LocalMaeMolSupplier * __ptr64)
        """
    def atEnd(self) -> bool:
        """
            Returns whether or not we have hit EOF.
            
        
            C++ signature :
                bool atEnd(class `anonymous namespace'::LocalMaeMolSupplier {lvalue})
        """
    def reset(self) -> None:
        """
            Resets our position in the file to the beginning.
            
        
            C++ signature :
                void reset(class `anonymous namespace'::LocalMaeMolSupplier {lvalue})
        """
class MaeWriter(Boost.Python.instance):
    """
    An experimental class for writing molecules to Maestro files.
    
      Usage examples:
    
        1) writing to a named file:
    
           >>> writer = MaeWriter('out.mae')
           >>> for mol in list_of_mols:
           ...    writer.write(mol)
    
        2) writing to a file-like object: 
    
           >>> import gzip
           >>> outf=gzip.open('out.mae.gz','wt+')
           >>> writer = MaeWriter(outf)
           >>> for mol in list_of_mols:
           ...   writer.write(mol)
           >>> writer.close()
           >>> outf.close()
    
      By default all non-private molecule, atom and bond properties are written
      to the Maestro file. This can be changed using the SetProps method:
    
           >>> writer = MaeWriter('out.mae')
           >>> writer.SetProps(['prop1','prop2'])
    
      Properties that are specified, but are not present will be ignored.
    
      Kekulization is mandatory, as the Maestro format does not have
      the concept of an aromatic bond
    
      As this is an experimental writer, many features are not supported yet,
      e.g. chirality and bond stereo labels, stereo groups, substance groups,
      isotopes, or even dummy atoms. Note that these aren't supported by
      MaeMolSupplier either.
    
     
    """
    @staticmethod
    def GetText(mol: Mol, confId: int = -1, props_list: ..., structstd: ..., classstd: ... = ..., structstd: ..., classstd: ...) -> str:
        """
            returns the Maestro ct block text for a molecule
        
            C++ signature :
                class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > GetText(class RDKit::ROMol [,int=-1 [,class std::vector<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::allocator<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > > >=<rdkit.rdBase._vectclass std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > object at 0x00000171C584E540>]])
        """
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def NumMols(self) -> int:
        """
            Returns the number of molecules written so far.
            
            
        
            C++ signature :
                unsigned int NumMols(class RDKit::LocalMaeWriter {lvalue})
        """
    def SetProps(self, props_list: ..., structstd: ..., classstd: ...) -> None:
        """
            Sets the atom and mol properties to be written to the output file
            
              ARGUMENTS:
            
                - props: a list or tuple of atom and mol property names
            
            
        
            C++ signature :
                void SetProps(class RDKit::LocalMaeWriter {lvalue},class std::vector<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class std::allocator<class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > > >)
        """
    def __enter__(self) -> MaeWriter:
        """
            C++ signature :
                class RDKit::LocalMaeWriter * __ptr64 __enter__(class RDKit::LocalMaeWriter * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::LocalMaeWriter * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    @typing.overload
    def __init__(self, fileobj: typing.Any) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class boost::python::api::object {lvalue})
        """
    @typing.overload
    def __init__(self, streambuf: streambuf) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class boost_adaptbx::python::streambuf {lvalue})
        """
    @typing.overload
    def __init__(self, filename: str) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)
        """
    def close(self) -> None:
        """
            Flushes the output file and closes it. The Writer cannot be used after this.
            
            
        
            C++ signature :
                void close(class RDKit::LocalMaeWriter {lvalue})
        """
    def flush(self) -> None:
        """
            Flushes the output file (forces the disk file to be updated).
            
            
        
            C++ signature :
                void flush(class RDKit::LocalMaeWriter {lvalue})
        """
    def write(self, mol: Mol, confId: int = -1) -> None:
        """
            Writes a molecule to the output file.
            
              ARGUMENTS:
            
                - mol: the Mol to be written
                - confId: (optional) ID of the conformation to write
            
            
        
            C++ signature :
                void write(class RDKit::LocalMaeWriter {lvalue},class RDKit::ROMol [,int=-1])
        """
class MolFromSCSRParams(Boost.Python.instance):
    """
    Parameters controlling conversion of an SCSRMol to a Mol
    """
    __instance_size__: typing.ClassVar[int] = 40
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @property
    def includeLeavingGroups(*args, **kwargs):
        """
        include leaving groups atoms if not substited at that position
        """
    @includeLeavingGroups.setter
    def includeLeavingGroups(*args, **kwargs):
        ...
    @property
    def scsrBaseHbondOptions(*args, **kwargs):
        """
        One of Ignore, UseSapAll(default) , UseSapOne, Auto
        """
    @scsrBaseHbondOptions.setter
    def scsrBaseHbondOptions(*args, **kwargs):
        ...
    @property
    def scsrTemplateNames(*args, **kwargs):
        """
        If True, the first template name in the Sgroup is used as the Sgroup label
        """
    @scsrTemplateNames.setter
    def scsrTemplateNames(*args, **kwargs):
        ...
class MolWriterParams(Boost.Python.instance):
    """
    Parameters controlling Mol writing
    """
    __instance_size__: typing.ClassVar[int] = 32
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    @staticmethod
    def __setattr__(arg1: typing.Any, arg2: str, arg3: typing.Any) -> None:
        """
            C++ signature :
                void __setattr__(class boost::python::api::object,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class boost::python::api::object)
        """
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @property
    def forceV3000(*args, **kwargs):
        """
        force generation a V3000 mol block (happens automatically with more than 999 atoms or bonds)(default=False)
        """
    @forceV3000.setter
    def forceV3000(*args, **kwargs):
        ...
    @property
    def includeStereo(*args, **kwargs):
        """
        toggles inclusion of stereochemistry information (default=True)
        """
    @includeStereo.setter
    def includeStereo(*args, **kwargs):
        ...
    @property
    def kekulize(*args, **kwargs):
        """
        triggers kekulization of the molecule before it is written (default=True)
        """
    @kekulize.setter
    def kekulize(*args, **kwargs):
        ...
    @property
    def precision(*args, **kwargs):
        """
        precision of coordinates (only available in V3000)(default=false)
        """
    @precision.setter
    def precision(*args, **kwargs):
        ...
class MultithreadedSDMolSupplier(Boost.Python.instance):
    """
    A class which concurrently supplies molecules from a text file.
      Please note that this class is still a bit experimental and the API may
      change in future releases.
    
      Usage examples:
    
        1) Lazy evaluation: the molecules might not be constructed until we ask for them:
    
           >>> suppl = MultithreadedSDMolSupplier('in.sdf')
           >>> for mol in suppl:
           ...    if(mol):
           ...      mol.GetNumAtoms()
    
        2) Lazy evaluation 2:
    
           >>> suppl = MultithreadedSDMolSupplier('in.sdf')
           >>> while (!suppl.atEnd()):
           ...    mol = next(mol)
           ...    if(mol):
           ...      mol.GetNumAtoms()
    
    """
    __instance_size__: typing.ClassVar[int] = 40
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def GetLastItemText(self) -> str:
        """
            Returns the text for the last extracted item.
            
        
            C++ signature :
                class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > GetLastItemText(class RDKit::v1::MultithreadedSDMolSupplier * __ptr64)
        """
    def GetLastRecordId(self) -> int:
        """
            Returns the record id for the last extracted item.
            
        
            C++ signature :
                unsigned int GetLastRecordId(class RDKit::v1::MultithreadedSDMolSupplier * __ptr64)
        """
    def GetProcessPropertyLists(self) -> bool:
        """
            returns whether or not any property lists that are present will be processed when reading molecules
        
            C++ signature :
                bool GetProcessPropertyLists(class RDKit::v1::MultithreadedSDMolSupplier {lvalue})
        """
    def SetProcessPropertyLists(self, val: bool) -> None:
        """
            sets whether or not any property lists that are present will be processed when reading molecules
        
            C++ signature :
                void SetProcessPropertyLists(class RDKit::v1::MultithreadedSDMolSupplier {lvalue},bool)
        """
    def __enter__(self) -> MultithreadedSDMolSupplier:
        """
            C++ signature :
                class RDKit::v1::MultithreadedSDMolSupplier * __ptr64 __enter__(class RDKit::v1::MultithreadedSDMolSupplier * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::v1::MultithreadedSDMolSupplier * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    @typing.overload
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @typing.overload
    def __init__(self, fileName: str, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True, numWriterThreads: int = 1, sizeInputQueue: int = 5, sizeOutputQueue: int = 5) -> None:
        """
            Constructor
            
              ARGUMENTS: 
            
                - fileName: name of the file to be read
            
                - sanitize: (optional) toggles sanitization of molecules as they are read.
                  Defaults to true.
            
                - removeHs: (optional) removes Hs. Defaults to true.
            
                - strictParsing: (optional) allows strict or lax parsing. Defaults to true.
            
                - numWriterThreads: (optional) number of writer threads. Defaults to 1.
            
                - sizeInputQueue: (optional) size of input/reader queue. Defaults to 5.
            
                - sizeOutputQueue: (optional) size of output/writer queue. Defaults to 5.
            
            
        
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,bool=True [,unsigned int=1 [,unsigned __int64=5 [,unsigned __int64=5]]]]]])
        """
    def __iter__(self) -> MultithreadedSDMolSupplier:
        """
            C++ signature :
                class RDKit::v1::MultithreadedSDMolSupplier * __ptr64 __iter__(class RDKit::v1::MultithreadedSDMolSupplier * __ptr64)
        """
    def __next__(self) -> rdkit.Chem.Mol:
        """
            Returns the next molecule in the file. Raises _StopIteration_ on EOF.
            
        
            C++ signature :
                class RDKit::ROMol * __ptr64 __next__(class RDKit::v1::MultithreadedSDMolSupplier * __ptr64)
        """
    def atEnd(self) -> bool:
        """
            Returns true if we have read all records else false.
            
        
            C++ signature :
                bool atEnd(class RDKit::v1::MultithreadedSDMolSupplier {lvalue})
        """
class MultithreadedSmilesMolSupplier(Boost.Python.instance):
    """
    A class which concurrently supplies molecules from a text file.
      Please note that this class is still a bit experimental and the API may
      change in future releases.
    
      Usage examples:
    
        1) Lazy evaluation: the molecules might not be constructed until we ask for them:
    
           >>> suppl = MultithreadedSmilesMolSupplier('in.smi')
           >>> for mol in suppl:
           ...    if(mol):
           ...      mol.GetNumAtoms()
    
        2) Lazy evaluation 2:
    
           >>> suppl = MultithreadedSmilesMolSupplier('in.smi')
           >>> while (!suppl.atEnd()):
           ...    mol = next(mol)
           ...    if(mol):
           ...      mol.GetNumAtoms()
    
    """
    __instance_size__: typing.ClassVar[int] = 40
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def GetLastItemText(self) -> str:
        """
            Returns the text for the last extracted item.
            
        
            C++ signature :
                class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > GetLastItemText(class RDKit::v1::MultithreadedSmilesMolSupplier * __ptr64)
        """
    def GetLastRecordId(self) -> int:
        """
            Returns the record id for the last extracted item.
            
        
            C++ signature :
                unsigned int GetLastRecordId(class RDKit::v1::MultithreadedSmilesMolSupplier * __ptr64)
        """
    def __enter__(self) -> MultithreadedSmilesMolSupplier:
        """
            C++ signature :
                class RDKit::v1::MultithreadedSmilesMolSupplier * __ptr64 __enter__(class RDKit::v1::MultithreadedSmilesMolSupplier * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::v1::MultithreadedSmilesMolSupplier * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    @typing.overload
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @typing.overload
    def __init__(self, fileName: str, delimiter: str = '\t', smilesColumn: int = 0, nameColumn: int = 1, titleLine: bool = True, sanitize: bool = True, numWriterThreads: int = 1, sizeInputQueue: int = 5, sizeOutputQueue: int = 5) -> None:
        """
            Constructor
            
              ARGUMENTS: 
            
                - fileName: name of the file to be read
            
                - delimiter: (optional) text delimiter (a string).  Defauts to ' 	'.
            
                - smilesColumn: (optional) index of the column containing the SMILES
                  data.  Defaults to 0.
            
                - nameColumn: (optional) index of the column containing molecule names.
                  Defaults to 1.
            
                - titleLine: (optional) set this toggle if the file contains a title line.
                  Defaults to true.
            
                - sanitize: (optional) toggles sanitization of molecules as they are read.
                  Defaults to true.
            
                - numWriterThreads: (optional) number of writer threads. Defaults to 1.
            
                - sizeInputQueue: (optional) size of input/reader queue. Defaults to 5.
            
                - sizeOutputQueue: (optional) size of output/writer queue. Defaults to 5.
            
            
        
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >=' \\t' [,int=0 [,int=1 [,bool=True [,bool=True [,unsigned int=1 [,unsigned __int64=5 [,unsigned __int64=5]]]]]]]])
        """
    def __iter__(self) -> MultithreadedSmilesMolSupplier:
        """
            C++ signature :
                class RDKit::v1::MultithreadedSmilesMolSupplier * __ptr64 __iter__(class RDKit::v1::MultithreadedSmilesMolSupplier * __ptr64)
        """
    def __next__(self) -> rdkit.Chem.Mol:
        """
            Returns the next molecule in the file. Raises _StopIteration_ on EOF.
            
        
            C++ signature :
                class RDKit::ROMol * __ptr64 __next__(class RDKit::v1::MultithreadedSmilesMolSupplier * __ptr64)
        """
    def atEnd(self) -> bool:
        """
            Returns true if we have read all records else false.
            
        
            C++ signature :
                bool atEnd(class RDKit::v1::MultithreadedSmilesMolSupplier {lvalue})
        """
class PDBWriter(Boost.Python.instance):
    """
    A class for writing molecules to PDB files.
    """
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def NumMols(self) -> int:
        """
            Returns the number of molecules written so far.
            
            
        
            C++ signature :
                unsigned int NumMols(class RDKit::PDBWriter {lvalue})
        """
    def __enter__(self) -> PDBWriter:
        """
            C++ signature :
                class RDKit::PDBWriter * __ptr64 __enter__(class RDKit::PDBWriter * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::PDBWriter * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    @typing.overload
    def __init__(self, fileObj: typing.Any, flavor: int = 0) -> typing.Any:
        """
            C++ signature :
                void * __ptr64 __init__(class boost::python::api::object,class boost::python::api::object {lvalue} [,unsigned int=0])
        """
    @typing.overload
    def __init__(self, fileName: str, flavor: int = 0) -> None:
        """
            Constructor.
            
               ARGUMENTS:
            
                 - fileName: name of the output file. ('-' to write to stdout)
                 - flavor: (optional) 
            
            
        
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,unsigned int=0])
        """
    def close(self) -> None:
        """
            Flushes the output file and closes it. The Writer cannot be used after this.
            
            
        
            C++ signature :
                void close(class RDKit::PDBWriter {lvalue})
        """
    def flush(self) -> None:
        """
            Flushes the output file (forces the disk file to be updated).
            
            
        
            C++ signature :
                void flush(class RDKit::PDBWriter {lvalue})
        """
    def write(self, mol: Mol, confId: int = -1) -> None:
        """
            Writes a molecule to the output file.
            
              ARGUMENTS:
            
                - mol: the Mol to be written
                - confId: (optional) ignored 
            
            
        
            C++ signature :
                void write(class RDKit::PDBWriter {lvalue},class RDKit::ROMol [,int=-1])
        """
class PNGMetadataParams(Boost.Python.instance):
    """
    Parameters controlling metadata included in PNG images
    """
    __instance_size__: typing.ClassVar[int] = 56
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @property
    def cxSmilesFlags(*args, **kwargs):
        """
        choose CXSMILES fields to be included in the CXSMILES string (default=rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ALL)
        """
    @cxSmilesFlags.setter
    def cxSmilesFlags(*args, **kwargs):
        ...
    @property
    def includeMol(*args, **kwargs):
        """
        toggles inclusion of molecule molblock (default=False)
        """
    @includeMol.setter
    def includeMol(*args, **kwargs):
        ...
    @property
    def includePkl(*args, **kwargs):
        """
        toggles inclusion of molecule pickle (default=True)
        """
    @includePkl.setter
    def includePkl(*args, **kwargs):
        ...
    @property
    def includeSmiles(*args, **kwargs):
        """
        toggles inclusion of molecule CXSMILES (default=True)
        """
    @includeSmiles.setter
    def includeSmiles(*args, **kwargs):
        ...
    @property
    def propertyFlags(*args, **kwargs):
        """
        choose properties to be included in the pickle (default=rdkit.Chem.rdchem.PropertyPickleOptions.NoProps)
        """
    @propertyFlags.setter
    def propertyFlags(*args, **kwargs):
        ...
    @property
    def restoreBondDirs(*args, **kwargs):
        """
        choose what to do with bond dirs in the CXSMILES string (default=rdkit.Chem.rdmolfiles.RestoreBondDirOption.RestoreBondDirOptionClear)
        """
    @restoreBondDirs.setter
    def restoreBondDirs(*args, **kwargs):
        ...
    @property
    def smilesWriteParams(*args, **kwargs):
        """
        choose SmilesWriteParams for the CXSMILES string (default=rdkit.Chem.rdmolfiles.SmilesWriteParams())
        """
    @smilesWriteParams.setter
    def smilesWriteParams(*args, **kwargs):
        ...
class RestoreBondDirOption(Boost.Python.enum):
    RestoreBondDirOptionClear: typing.ClassVar[RestoreBondDirOption]  # value = rdkit.Chem.rdmolfiles.RestoreBondDirOption.RestoreBondDirOptionClear
    RestoreBondDirOptionTrue: typing.ClassVar[RestoreBondDirOption]  # value = rdkit.Chem.rdmolfiles.RestoreBondDirOption.RestoreBondDirOptionTrue
    __slots__: typing.ClassVar[tuple] = tuple()
    names: typing.ClassVar[dict]  # value = {'RestoreBondDirOptionClear': rdkit.Chem.rdmolfiles.RestoreBondDirOption.RestoreBondDirOptionClear, 'RestoreBondDirOptionTrue': rdkit.Chem.rdmolfiles.RestoreBondDirOption.RestoreBondDirOptionTrue}
    values: typing.ClassVar[dict]  # value = {1: rdkit.Chem.rdmolfiles.RestoreBondDirOption.RestoreBondDirOptionClear, 0: rdkit.Chem.rdmolfiles.RestoreBondDirOption.RestoreBondDirOptionTrue}
class SCSRBaseHbondOptions(Boost.Python.enum):
    Auto: typing.ClassVar[SCSRBaseHbondOptions]  # value = rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.Auto
    Ignore: typing.ClassVar[SCSRBaseHbondOptions]  # value = rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.Ignore
    UseSapAll: typing.ClassVar[SCSRBaseHbondOptions]  # value = rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.UseSapAll
    UseSapOne: typing.ClassVar[SCSRBaseHbondOptions]  # value = rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.UseSapOne
    __slots__: typing.ClassVar[tuple] = tuple()
    names: typing.ClassVar[dict]  # value = {'Ignore': rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.Ignore, 'UseSapAll': rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.UseSapAll, 'UseSapOne': rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.UseSapOne, 'Auto': rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.Auto}
    values: typing.ClassVar[dict]  # value = {0: rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.Ignore, 1: rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.UseSapAll, 2: rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.UseSapOne, 3: rdkit.Chem.rdmolfiles.SCSRBaseHbondOptions.Auto}
class SCSRTemplateNames(Boost.Python.enum):
    AsEntered: typing.ClassVar[SCSRTemplateNames]  # value = rdkit.Chem.rdmolfiles.SCSRTemplateNames.AsEntered
    UseFirstName: typing.ClassVar[SCSRTemplateNames]  # value = rdkit.Chem.rdmolfiles.SCSRTemplateNames.UseFirstName
    UseSecondName: typing.ClassVar[SCSRTemplateNames]  # value = rdkit.Chem.rdmolfiles.SCSRTemplateNames.UseSecondName
    __slots__: typing.ClassVar[tuple] = tuple()
    names: typing.ClassVar[dict]  # value = {'UseFirstName': rdkit.Chem.rdmolfiles.SCSRTemplateNames.UseFirstName, 'UseSecondName': rdkit.Chem.rdmolfiles.SCSRTemplateNames.UseSecondName, 'AsEntered': rdkit.Chem.rdmolfiles.SCSRTemplateNames.AsEntered}
    values: typing.ClassVar[dict]  # value = {1: rdkit.Chem.rdmolfiles.SCSRTemplateNames.UseFirstName, 2: rdkit.Chem.rdmolfiles.SCSRTemplateNames.UseSecondName, 0: rdkit.Chem.rdmolfiles.SCSRTemplateNames.AsEntered}
class SDMolSupplier(Boost.Python.instance):
    """
    A class which supplies molecules from an SD file.
    
      Usage examples:
    
        1) Lazy evaluation: the molecules are not constructed until we ask for them:
    
           >>> suppl = SDMolSupplier('in.sdf')
           >>> for mol in suppl:
           ...    mol.GetNumAtoms()
    
        2) Lazy evaluation 2:
    
           >>> suppl = SDMolSupplier('in.sdf')
           >>> mol1 = next(suppl)
           >>> mol2 = next(suppl)
           >>> suppl.reset()
           >>> mol3 = next(suppl)
           # mol3 and mol1 are the same:
           >>> MolToSmiles(mol3)==MolToSmiles(mol1)
    
        3) Random Access:
    
           >>> suppl = SDMolSupplier('in.sdf')
           >>> mol1 = suppl[0] 
           >>> mol2 = suppl[1] 
           # NOTE: this will generate an IndexError if the supplier doesn't have that many
           molecules.
    
        4) Random Access 2:  looping over all molecules 
    
           >>> suppl = SDMolSupplier('in.sdf')
           >>> nMols = len(suppl)
           >>> for i in range(nMols):
           ...   suppl[i].GetNumAtoms()
    
      Properties in the SD file are used to set properties on each molecule.
      The properties are accessible using the mol.GetProp(propName) method.
    
    """
    __instance_size__: typing.ClassVar[int] = 40
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def GetItemText(self, index: int) -> str:
        """
            returns the text for an item
        
            C++ signature :
                class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > GetItemText(class RDKit::v1::SDMolSupplier {lvalue},unsigned int)
        """
    def GetProcessPropertyLists(self) -> bool:
        """
            returns whether or not any property lists that are present will be processed when reading molecules
        
            C++ signature :
                bool GetProcessPropertyLists(class RDKit::v1::SDMolSupplier {lvalue})
        """
    def SetData(self, data: str, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True) -> None:
        """
            Sets the text to be parsed
        
            C++ signature :
                void SetData(class RDKit::v1::SDMolSupplier {lvalue},class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,bool=True]]])
        """
    def SetProcessPropertyLists(self, val: bool) -> None:
        """
            sets whether or not any property lists that are present will be processed when reading molecules
        
            C++ signature :
                void SetProcessPropertyLists(class RDKit::v1::SDMolSupplier {lvalue},bool)
        """
    def _SetStreamIndices(self, locs: typing.Any) -> None:
        """
            Sets the locations of mol beginnings in the input stream. Be *very* careful with this method.
        
            C++ signature :
                void _SetStreamIndices(class RDKit::v1::SDMolSupplier {lvalue},class boost::python::api::object)
        """
    def __enter__(self) -> SDMolSupplier:
        """
            C++ signature :
                class RDKit::v1::SDMolSupplier * __ptr64 __enter__(class RDKit::v1::SDMolSupplier * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::v1::SDMolSupplier * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    def __getitem__(self, idx: int) -> rdkit.Chem.Mol:
        """
            C++ signature :
                class RDKit::ROMol * __ptr64 __getitem__(class RDKit::v1::SDMolSupplier * __ptr64,int)
        """
    @typing.overload
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @typing.overload
    def __init__(self, fileName: str, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,bool=True]]])
        """
    def __iter__(self) -> SDMolSupplier:
        """
            C++ signature :
                class RDKit::v1::SDMolSupplier * __ptr64 __iter__(class RDKit::v1::SDMolSupplier * __ptr64)
        """
    def __len__(self) -> int:
        """
            C++ signature :
                unsigned int __len__(class RDKit::v1::SDMolSupplier {lvalue})
        """
    def __next__(self) -> rdkit.Chem.Mol:
        """
            Returns the next molecule in the file.  Raises _StopIteration_ on EOF.
            
        
            C++ signature :
                class RDKit::ROMol * __ptr64 __next__(class RDKit::v1::SDMolSupplier * __ptr64)
        """
    def atEnd(self) -> bool:
        """
            Returns whether or not we have hit EOF.
            
        
            C++ signature :
                bool atEnd(class RDKit::v1::SDMolSupplier {lvalue})
        """
    def reset(self) -> None:
        """
            Resets our position in the file to the beginning.
            
        
            C++ signature :
                void reset(class RDKit::v1::SDMolSupplier {lvalue})
        """
class SDWriter(Boost.Python.instance):
    """
    A class for writing molecules to SD files.
    
      Usage examples:
    
        1) writing to a named file:
    
           >>> writer = SDWriter('out.sdf')
           >>> for mol in list_of_mols:
           ...    writer.write(mol)
    
        2) writing to a file-like object: 
    
           >>> import gzip
           >>> outf=gzip.open('out.sdf.gz','wt+')
           >>> writer = SDWriter(outf)
           >>> for mol in list_of_mols:
           ...   writer.write(mol)
           >>> writer.close()
           >>> outf.close()
    
      By default all non-private molecular properties are written to the SD file.
      This can be changed using the SetProps method:
    
           >>> writer = SDWriter('out.sdf')
           >>> writer.SetProps(['prop1','prop2'])
    
    """
    @staticmethod
    def GetText(mol: Mol, confId: int = -1, kekulize: bool = True, force_v3000: bool = False, molid: int = -1) -> str:
        """
            returns the SD text for a molecule
        
            C++ signature :
                class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > GetText(class RDKit::ROMol [,int=-1 [,bool=True [,bool=False [,int=-1]]]])
        """
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def GetForceV3000(self) -> bool:
        """
            Returns whether or not V3000 mol file writing is being forced.
            
            
        
            C++ signature :
                bool GetForceV3000(class RDKit::SDWriter {lvalue})
        """
    def GetKekulize(self) -> bool:
        """
            Returns whether or not molecules are kekulized on writing.
            
            
        
            C++ signature :
                bool GetKekulize(class RDKit::SDWriter {lvalue})
        """
    def NumMols(self) -> int:
        """
            Returns the number of molecules written so far.
            
            
        
            C++ signature :
                unsigned int NumMols(class RDKit::SDWriter {lvalue})
        """
    def SetForceV3000(self, val: bool) -> None:
        """
            Sets whether or not V3000 mol file writing is being forced.
            
            
        
            C++ signature :
                void SetForceV3000(class RDKit::SDWriter {lvalue},bool)
        """
    def SetKekulize(self, val: bool) -> None:
        """
            Sets whether or not molecules are kekulized on writing.
            
            
        
            C++ signature :
                void SetKekulize(class RDKit::SDWriter {lvalue},bool)
        """
    def SetProps(self, props: typing.Any) -> None:
        """
            Sets the properties to be written to the output file
            
              ARGUMENTS:
            
                - props: a list or tuple of property names
            
            
        
            C++ signature :
                void SetProps(class RDKit::SDWriter {lvalue},class boost::python::api::object)
        """
    def __enter__(self) -> SDWriter:
        """
            C++ signature :
                class RDKit::SDWriter * __ptr64 __enter__(class RDKit::SDWriter * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::SDWriter * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    @typing.overload
    def __init__(self, arg1: typing.Any) -> typing.Any:
        """
            C++ signature :
                void * __ptr64 __init__(class boost::python::api::object,class boost::python::api::object {lvalue})
        """
    @typing.overload
    def __init__(self, fileName: str) -> None:
        """
            Constructor.
            
               If a string argument is provided, it will be treated as the name of the output file.
               If a file-like object is provided, output will be sent there.
            
            
        
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)
        """
    def close(self) -> None:
        """
            Flushes the output file and closes it. The Writer cannot be used after this.
            
            
        
            C++ signature :
                void close(class RDKit::SDWriter {lvalue})
        """
    def flush(self) -> None:
        """
            Flushes the output file (forces the disk file to be updated).
            
            
        
            C++ signature :
                void flush(class RDKit::SDWriter {lvalue})
        """
    def write(self, mol: Mol, confId: int = -1) -> None:
        """
            Writes a molecule to the output file.
            
              ARGUMENTS:
            
                - mol: the Mol to be written
                - confId: (optional) ID of the conformation to write
            
            
        
            C++ signature :
                void write(class RDKit::SDWriter {lvalue},class RDKit::ROMol {lvalue} [,int=-1])
        """
class SmartsParserParams(Boost.Python.instance):
    """
    Parameters controlling SMARTS Parsing
    """
    __instance_size__: typing.ClassVar[int] = 48
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    @staticmethod
    def __setattr__(arg1: typing.Any, arg2: str, arg3: typing.Any) -> None:
        """
            C++ signature :
                void __setattr__(class boost::python::api::object,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class boost::python::api::object)
        """
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @property
    def allowCXSMILES(*args, **kwargs):
        """
        controls whether or not the CXSMILES extensions are parsed
        """
    @allowCXSMILES.setter
    def allowCXSMILES(*args, **kwargs):
        ...
    @property
    def debugParse(*args, **kwargs):
        """
        controls the amount of debugging information produced
        """
    @debugParse.setter
    def debugParse(*args, **kwargs):
        ...
    @property
    def mergeHs(*args, **kwargs):
        """
        toggles merging H atoms in the SMARTS into neighboring atoms
        """
    @mergeHs.setter
    def mergeHs(*args, **kwargs):
        ...
    @property
    def parseName(*args, **kwargs):
        """
        controls whether or not the molecule name is also parsed
        """
    @parseName.setter
    def parseName(*args, **kwargs):
        ...
    @property
    def strictCXSMILES(*args, **kwargs):
        """
        controls whether or not problems in CXSMILES parsing causes molecule parsing to fail
        """
    @strictCXSMILES.setter
    def strictCXSMILES(*args, **kwargs):
        ...
class SmilesMolSupplier(Boost.Python.instance):
    """
    A class which supplies molecules from a text file.
    
      Usage examples:
    
        1) Lazy evaluation: the molecules are not constructed until we ask for them:
    
           >>> suppl = SmilesMolSupplier('in.smi')
           >>> for mol in suppl:
           ...    mol.GetNumAtoms()
    
        2) Lazy evaluation 2:
    
           >>> suppl = SmilesMolSupplier('in.smi')
           >>> mol1 = next(suppl)
           >>> mol2 = next(suppl)
           >>> suppl.reset()
           >>> mol3 = next(suppl)
           # mol3 and mol1 are the same:
           >>> MolToSmiles(mol3)==MolToSmiles(mol1)
    
        3) Random Access:  all molecules are constructed as soon as we ask for the
           length:
    
           >>> suppl = SmilesMolSupplier('in.smi')
           >>> nMols = len(suppl)
           >>> for i in range(nMols):
           ...   suppl[i].GetNumAtoms()
    
      If the input file has a title line and more than two columns (smiles and id), the
      additional columns will be used to set properties on each molecule.  The properties
      are accessible using the mol.GetProp(propName) method.
    
    """
    __instance_size__: typing.ClassVar[int] = 40
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def GetItemText(self, index: int) -> str:
        """
            returns the text for an item
        
            C++ signature :
                class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > GetItemText(class RDKit::v1::SmilesMolSupplier {lvalue},unsigned int)
        """
    def SetData(self, data: str, delimiter: str = '', smilesColumn: int = 0, nameColumn: int = 1, titleLine: bool = True, sanitize: bool = True) -> None:
        """
            Sets the text to be parsed
        
            C++ signature :
                void SetData(class RDKit::v1::SmilesMolSupplier {lvalue},class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >=' ' [,int=0 [,int=1 [,bool=True [,bool=True]]]]])
        """
    def __enter__(self) -> SmilesMolSupplier:
        """
            C++ signature :
                class RDKit::v1::SmilesMolSupplier * __ptr64 __enter__(class RDKit::v1::SmilesMolSupplier * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::v1::SmilesMolSupplier * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    def __getitem__(self, idx: int) -> rdkit.Chem.Mol:
        """
            C++ signature :
                class RDKit::ROMol * __ptr64 __getitem__(class RDKit::v1::SmilesMolSupplier * __ptr64,int)
        """
    @typing.overload
    def __init__(self, data: str, delimiter: str = '', smilesColumn: int = 0, nameColumn: int = 1, titleLine: bool = True, sanitize: bool = True) -> None:
        """
            Constructor
            
              ARGUMENTS: 
            
                - fileName: name of the file to be read
            
                - delimiter: (optional) text delimiter (a string).  Defauts to ' '.
            
                - smilesColumn: (optional) index of the column containing the SMILES
                  data.  Defaults to 0.
            
                - nameColumn: (optional) index of the column containing molecule names.
                  Defaults to 1.
            
                - titleLine: (optional) set this toggle if the file contains a title line.
                  Defaults to 1.
            
                - sanitize: (optional) toggles sanitization of molecules as they are read.
                  Defaults to 1.
            
            
        
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >=' ' [,int=0 [,int=1 [,bool=True [,bool=True]]]]])
        """
    @typing.overload
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    def __iter__(self) -> SmilesMolSupplier:
        """
            C++ signature :
                class RDKit::v1::SmilesMolSupplier * __ptr64 __iter__(class RDKit::v1::SmilesMolSupplier * __ptr64)
        """
    def __len__(self) -> int:
        """
            C++ signature :
                unsigned int __len__(class RDKit::v1::SmilesMolSupplier {lvalue})
        """
    def __next__(self) -> rdkit.Chem.Mol:
        """
            Returns the next molecule in the file.  Raises _StopIteration_ on EOF.
            
        
            C++ signature :
                class RDKit::ROMol * __ptr64 __next__(class RDKit::v1::SmilesMolSupplier * __ptr64)
        """
    def reset(self) -> None:
        """
            Resets our position in the file to the beginning.
            
        
            C++ signature :
                void reset(class RDKit::v1::SmilesMolSupplier {lvalue})
        """
class SmilesParserParams(Boost.Python.instance):
    """
    Parameters controlling SMILES Parsing
    """
    __instance_size__: typing.ClassVar[int] = 48
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    @staticmethod
    def __setattr__(arg1: typing.Any, arg2: str, arg3: typing.Any) -> None:
        """
            C++ signature :
                void __setattr__(class boost::python::api::object,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class boost::python::api::object)
        """
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @property
    def allowCXSMILES(*args, **kwargs):
        """
        controls whether or not the CXSMILES extensions are parsed
        """
    @allowCXSMILES.setter
    def allowCXSMILES(*args, **kwargs):
        ...
    @property
    def debugParse(*args, **kwargs):
        """
        controls the amount of debugging information produced
        """
    @debugParse.setter
    def debugParse(*args, **kwargs):
        ...
    @property
    def parseName(*args, **kwargs):
        """
        controls whether or not the molecule name is also parsed
        """
    @parseName.setter
    def parseName(*args, **kwargs):
        ...
    @property
    def removeHs(*args, **kwargs):
        """
        controls whether or not Hs are removed before the molecule is returned
        """
    @removeHs.setter
    def removeHs(*args, **kwargs):
        ...
    @property
    def sanitize(*args, **kwargs):
        """
        controls whether or not the molecule is sanitized before being returned
        """
    @sanitize.setter
    def sanitize(*args, **kwargs):
        ...
    @property
    def strictCXSMILES(*args, **kwargs):
        """
        controls whether or not problems in CXSMILES parsing causes molecule parsing to fail
        """
    @strictCXSMILES.setter
    def strictCXSMILES(*args, **kwargs):
        ...
class SmilesWriteParams(Boost.Python.instance):
    """
    Parameters controlling SMILES writing
    """
    __instance_size__: typing.ClassVar[int] = 40
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    @staticmethod
    def __setattr__(arg1: typing.Any, arg2: str, arg3: typing.Any) -> None:
        """
            C++ signature :
                void __setattr__(class boost::python::api::object,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,class boost::python::api::object)
        """
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @property
    def allBondsExplicit(*args, **kwargs):
        """
        include symbols for all bonds
        """
    @allBondsExplicit.setter
    def allBondsExplicit(*args, **kwargs):
        ...
    @property
    def allHsExplicit(*args, **kwargs):
        """
        provide hydrogen counts for every atom
        """
    @allHsExplicit.setter
    def allHsExplicit(*args, **kwargs):
        ...
    @property
    def canonical(*args, **kwargs):
        """
        generate canonical SMILES
        """
    @canonical.setter
    def canonical(*args, **kwargs):
        ...
    @property
    def cleanStereo(*args, **kwargs):
        """
        chiral centers are removed if they have duplicate sidechains
        """
    @cleanStereo.setter
    def cleanStereo(*args, **kwargs):
        ...
    @property
    def doIsomericSmiles(*args, **kwargs):
        """
        include stereochemistry and isotope information
        """
    @doIsomericSmiles.setter
    def doIsomericSmiles(*args, **kwargs):
        ...
    @property
    def doKekule(*args, **kwargs):
        """
        kekulize the molecule before generating the SMILES and output single/double bonds. NOTE that the output is not canonical and that this will thrown an exception if the molecule cannot be kekulized
        """
    @doKekule.setter
    def doKekule(*args, **kwargs):
        ...
    @property
    def doRandom(*args, **kwargs):
        """
        randomize the output order. The resulting SMILES is not canonical
        """
    @doRandom.setter
    def doRandom(*args, **kwargs):
        ...
    @property
    def ignoreAtomMapNumbers(*args, **kwargs):
        """
        ignore atom map numbers when canonicalizing the molecule
        """
    @ignoreAtomMapNumbers.setter
    def ignoreAtomMapNumbers(*args, **kwargs):
        ...
    @property
    def includeDativeBonds(*args, **kwargs):
        """
        include the RDKit extension for dative bonds. Otherwise dative bonds will be written as single bonds
        """
    @includeDativeBonds.setter
    def includeDativeBonds(*args, **kwargs):
        ...
    @property
    def rootedAtAtom(*args, **kwargs):
        """
        make sure the SMILES starts at the specified atom. The resulting SMILES is not canonical
        """
    @rootedAtAtom.setter
    def rootedAtAtom(*args, **kwargs):
        ...
class SmilesWriter(Boost.Python.instance):
    """
    A class for writing molecules to text files.
    """
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def NumMols(self) -> int:
        """
            Returns the number of molecules written so far.
            
            
        
            C++ signature :
                unsigned int NumMols(class RDKit::SmilesWriter {lvalue})
        """
    def SetProps(self, props: typing.Any) -> None:
        """
            Sets the properties to be written to the output file
            
              ARGUMENTS:
            
                - props: a list or tuple of property names
            
            
        
            C++ signature :
                void SetProps(class RDKit::SmilesWriter {lvalue},class boost::python::api::object)
        """
    def __enter__(self) -> SmilesWriter:
        """
            C++ signature :
                class RDKit::SmilesWriter * __ptr64 __enter__(class RDKit::SmilesWriter * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::SmilesWriter * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    @typing.overload
    def __init__(self, fileObj: typing.Any, delimiter: str = '', nameHeader: str = 'Name', includeHeader: bool = True, isomericSmiles: bool = True, kekuleSmiles: bool = False) -> typing.Any:
        """
            C++ signature :
                void * __ptr64 __init__(class boost::python::api::object,class boost::python::api::object {lvalue} [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >=' ' [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='Name' [,bool=True [,bool=True [,bool=False]]]]])
        """
    @typing.overload
    def __init__(self, fileName: str, delimiter: str = '', nameHeader: str = 'Name', includeHeader: bool = True, isomericSmiles: bool = True, kekuleSmiles: bool = False) -> None:
        """
            Constructor.
            
               ARGUMENTS:
            
                 - fileName: name of the output file. ('-' to write to stdout)
                 - delimiter: (optional) delimiter to be used to separate entries on each line.
                 - nameHeader: (optional) text to use for the name column in the header line.
                               If this is blank, names will not be included in the output.
                 - includeHeader: (optional) toggles inclusion of a header line in the output file.
                 - isomericSmiles: (optional) toggles output of isomeric smiles (includes stereochem information).
                 - kekuleSmiles: (optional) toggles output of kekule smiles (no aromatic bonds for molecules that have been kekulized).
            
            
        
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >=' ' [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='Name' [,bool=True [,bool=True [,bool=False]]]]])
        """
    def close(self) -> None:
        """
            Flushes the output file and closes it. The Writer cannot be used after this.
            
            
        
            C++ signature :
                void close(class RDKit::SmilesWriter {lvalue})
        """
    def flush(self) -> None:
        """
            Flushes the output file (forces the disk file to be updated).
            
            
        
            C++ signature :
                void flush(class RDKit::SmilesWriter {lvalue})
        """
    def write(self, mol: Mol, confId: int = -1) -> None:
        """
            Writes a molecule to the output file.
            
              ARGUMENTS:
            
                - mol: the Mol to be written
                - confId: (optional) ignored 
            
            
        
            C++ signature :
                void write(class RDKit::SmilesWriter {lvalue},class RDKit::ROMol [,int=-1])
        """
class TDTMolSupplier(Boost.Python.instance):
    """
    A class which supplies molecules from a TDT file.
    
      Usage examples:
    
        1) Lazy evaluation: the molecules are not constructed until we ask for them:
    
           >>> suppl = TDTMolSupplier('in.smi')
           >>> for mol in suppl:
           ...    mol.GetNumAtoms()
    
        2) Lazy evaluation 2:
    
           >>> suppl = TDTMolSupplier('in.smi')
           >>> mol1 = next(suppl)
           >>> mol2 = next(suppl)
           >>> suppl.reset()
           >>> mol3 = next(suppl)
    
           # mol3 and mol1 are the same:       >>> MolToSmiles(mol3)==MolToSmiles(mol1)
    
        3) Random Access:  all molecules are constructed as soon as we ask for the
           length:
    
           >>> suppl = TDTMolSupplier('in.smi')
           >>> nMols = len(suppl)
           >>> for i in range(nMols):
           ...   suppl[i].GetNumAtoms()
    
      Properties in the file are used to set properties on each molecule.
      The properties are accessible using the mol.GetProp(propName) method.
    
    """
    __instance_size__: typing.ClassVar[int] = 40
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def GetItemText(self, index: int) -> str:
        """
            returns the text for an item
        
            C++ signature :
                class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > GetItemText(class RDKit::v1::TDTMolSupplier {lvalue},unsigned int)
        """
    def SetData(self, data: str, nameRecord: str = '', confId2D: int = -1, confId3D: int = -1, sanitize: bool = True) -> None:
        """
            Sets the text to be parsed
        
            C++ signature :
                void SetData(class RDKit::v1::TDTMolSupplier {lvalue},class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='' [,int=-1 [,int=-1 [,bool=True]]]])
        """
    def __enter__(self) -> TDTMolSupplier:
        """
            C++ signature :
                class RDKit::v1::TDTMolSupplier * __ptr64 __enter__(class RDKit::v1::TDTMolSupplier * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::v1::TDTMolSupplier * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    def __getitem__(self, idx: int) -> rdkit.Chem.Mol:
        """
            C++ signature :
                class RDKit::ROMol * __ptr64 __getitem__(class RDKit::v1::TDTMolSupplier * __ptr64,int)
        """
    @typing.overload
    def __init__(self) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64)
        """
    @typing.overload
    def __init__(self, fileName: str, nameRecord: str = '', confId2D: int = -1, confId3D: int = -1, sanitize: bool = True) -> None:
        """
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='' [,int=-1 [,int=-1 [,bool=True]]]])
        """
    def __iter__(self) -> TDTMolSupplier:
        """
            C++ signature :
                class RDKit::v1::TDTMolSupplier * __ptr64 __iter__(class RDKit::v1::TDTMolSupplier * __ptr64)
        """
    def __len__(self) -> int:
        """
            C++ signature :
                unsigned int __len__(class RDKit::v1::TDTMolSupplier {lvalue})
        """
    def __next__(self) -> rdkit.Chem.Mol:
        """
            Returns the next molecule in the file.  Raises _StopIteration_ on EOF.
            
        
            C++ signature :
                class RDKit::ROMol * __ptr64 __next__(class RDKit::v1::TDTMolSupplier * __ptr64)
        """
    def reset(self) -> None:
        """
            Resets our position in the file to the beginning.
            
        
            C++ signature :
                void reset(class RDKit::v1::TDTMolSupplier {lvalue})
        """
class TDTWriter(Boost.Python.instance):
    """
    A class for writing molecules to TDT files.
    """
    @staticmethod
    def __reduce__(*args, **kwargs):
        ...
    def GetNumDigits(self) -> int:
        """
            C++ signature :
                unsigned int GetNumDigits(class RDKit::TDTWriter {lvalue})
        """
    def GetWrite2D(self) -> bool:
        """
            C++ signature :
                bool GetWrite2D(class RDKit::TDTWriter {lvalue})
        """
    def GetWriteNames(self) -> bool:
        """
            C++ signature :
                bool GetWriteNames(class RDKit::TDTWriter {lvalue})
        """
    def NumMols(self) -> int:
        """
            Returns the number of molecules written so far.
            
            
        
            C++ signature :
                unsigned int NumMols(class RDKit::TDTWriter {lvalue})
        """
    def SetNumDigits(self, numDigits: int) -> None:
        """
            sets the number of digits to be written for coordinates
        
            C++ signature :
                void SetNumDigits(class RDKit::TDTWriter {lvalue},unsigned int)
        """
    def SetProps(self, props: typing.Any) -> None:
        """
            Sets the properties to be written to the output file
            
              ARGUMENTS:
            
                - props: a list or tuple of property names
            
            
        
            C++ signature :
                void SetProps(class RDKit::TDTWriter {lvalue},class boost::python::api::object)
        """
    def SetWrite2D(self, state: bool = True) -> None:
        """
            causes 2D conformations to be written (default is 3D conformations)
        
            C++ signature :
                void SetWrite2D(class RDKit::TDTWriter {lvalue} [,bool=True])
        """
    def SetWriteNames(self, state: bool = True) -> None:
        """
            causes names to be written to the output file as NAME records
        
            C++ signature :
                void SetWriteNames(class RDKit::TDTWriter {lvalue} [,bool=True])
        """
    def __enter__(self) -> TDTWriter:
        """
            C++ signature :
                class RDKit::TDTWriter * __ptr64 __enter__(class RDKit::TDTWriter * __ptr64)
        """
    def __exit__(self, exc_type: typing.Any, exc_value: typing.Any, traceback: typing.Any) -> bool:
        """
            C++ signature :
                bool __exit__(class RDKit::TDTWriter * __ptr64,class boost::python::api::object,class boost::python::api::object,class boost::python::api::object)
        """
    @typing.overload
    def __init__(self, arg1: typing.Any) -> typing.Any:
        """
            C++ signature :
                void * __ptr64 __init__(class boost::python::api::object,class boost::python::api::object {lvalue})
        """
    @typing.overload
    def __init__(self, fileName: str) -> None:
        """
            Constructor.
            
               If a string argument is provided, it will be treated as the name of the output file.
               If a file-like object is provided, output will be sent there.
            
            
        
            C++ signature :
                void __init__(struct _object * __ptr64,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)
        """
    def close(self) -> None:
        """
            Flushes the output file and closes it. The Writer cannot be used after this.
            
            
        
            C++ signature :
                void close(class RDKit::TDTWriter {lvalue})
        """
    def flush(self) -> None:
        """
            Flushes the output file (forces the disk file to be updated).
            
            
        
            C++ signature :
                void flush(class RDKit::TDTWriter {lvalue})
        """
    def write(self, mol: Mol, confId: int = -1) -> None:
        """
            Writes a molecule to the output file.
            
              ARGUMENTS:
            
                - mol: the Mol to be written
                - confId: (optional) ID of the conformation to write
            
            
        
            C++ signature :
                void write(class RDKit::TDTWriter {lvalue},class RDKit::ROMol [,int=-1])
        """
def AddMetadataToPNGFile(metadata: dict, filename: typing.Any) -> typing.Any:
    """
        Adds metadata to PNG data read from a file.
        
             ARGUMENTS:
        
               - metadata: dict with the metadata to be written
                           (keys and values should be strings)
        
               - filename: the PNG filename
        
             RETURNS:
               the updated PNG data
    
        C++ signature :
            class boost::python::api::object AddMetadataToPNGFile(class boost::python::dict,class boost::python::api::object)
    """
def AddMetadataToPNGString(metadata: dict, png: typing.Any) -> typing.Any:
    """
        Adds metadata to a PNG string.
        
             ARGUMENTS:
        
               - metadata: dict with the metadata to be written
                           (keys and values should be strings)
        
               - png: the PNG string
        
             RETURNS:
               the updated PNG data
    
        C++ signature :
            class boost::python::api::object AddMetadataToPNGString(class boost::python::dict,class boost::python::api::object)
    """
def AtomFromSmarts(SMARTS: str) -> rdkit.Chem.Atom:
    """
        Construct an atom from a SMARTS string
    
        C++ signature :
            class RDKit::Atom * __ptr64 AtomFromSmarts(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)
    """
def AtomFromSmiles(SMILES: str) -> rdkit.Chem.Atom:
    """
        Construct an atom from a SMILES string
    
        C++ signature :
            class RDKit::Atom * __ptr64 AtomFromSmiles(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)
    """
def BondFromSmarts(SMILES: str) -> rdkit.Chem.Bond:
    """
        Construct a bond from a SMARTS string
    
        C++ signature :
            class RDKit::Bond * __ptr64 BondFromSmarts(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)
    """
def BondFromSmiles(SMILES: str) -> rdkit.Chem.Bond:
    """
        Construct a bond from a SMILES string
    
        C++ signature :
            class RDKit::Bond * __ptr64 BondFromSmiles(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >)
    """
def CanonicalRankAtoms(*args, **kwargs) -> ...:
    """
        Returns the canonical atom ranking for each atom of a molecule fragment.
          If breakTies is False, this returns the symmetry class for each atom.  The symmetry
          class is used by the canonicalization routines to type each atom based on the whole
          chemistry of the molecular graph.  Any atom with the same rank (symmetry class) is
          indistinguishable.  For example:
        
            >>> mol = MolFromSmiles('C1NCN1')
            >>> list(CanonicalRankAtoms(mol, breakTies=False))
            [0,1,0,1]
        
          In this case the carbons have the same symmetry class and the nitrogens have the same
          symmetry class.  From the perspective of the Molecular Graph, they are identical.
        
          ARGUMENTS:
        
            - mol: the molecule
            - breakTies: (optional) force breaking of ranked ties [default=True]
            - includeChirality: (optional) use chiral information when computing rank [default=True]
            - includeIsotopes: (optional) use isotope information when computing rank [default=True]
            - includeAtomMaps: (optional) use atom map information when computing rank [default=True]
            - includeChiralPresence: (optional) use information about whether or not chirality is specified when computing rank [default=False]
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::vector<unsigned int,class std::allocator<unsigned int> > CanonicalRankAtoms(class RDKit::ROMol [,bool=True [,bool=True [,bool=True [,bool=True [,bool=False]]]]])
    """
def CanonicalRankAtomsInFragment(mol: Mol, atomsToUse: typing.Any, bondsToUse: typing.Any = 0, atomSymbols: typing.Any = 0, breakTies: bool = True, includeChirality: bool = True, includeIsotopes: bool = True, includeAtomMaps: bool = True, includeChiralPresence: bool = False) -> _vectint:
    """
        Returns the canonical atom ranking for each atom of a molecule fragment
          See help(CanonicalRankAtoms) for more information.
        
           >>> mol = MolFromSmiles('C1NCN1.C1NCN1')
           >>> list(CanonicalRankAtomsInFragment(mol, atomsToUse=range(0,4), breakTies=False))
           [4,6,4,6,-1,-1,-1,-1]
           >>> list(CanonicalRankAtomsInFragment(mol, atomsToUse=range(4,8), breakTies=False))
           [-1,-1,-1,-1,4,6,4,6]
        
          ARGUMENTS:
        
            - mol: the molecule
            - atomsToUse : a list of atoms to include in the fragment
            - bondsToUse : (optional) a list of bonds to include in the fragment
              if not provided, all bonds between the atoms provided
              will be included.
            - atomSymbols : (optional) a list with the symbols to use for the atoms
              in the SMILES. This should have be mol.GetNumAtoms() long.
            - breakTies: (optional) force breaking of ranked ties
            - includeChirality: (optional) use chiral information when computing rank [default=True]
            - includeIsotopes: (optional) use isotope information when computing rank [default=True]
            - includeAtomMaps: (optional) use atom map information when computing rank [default=True]
            - includeChiralPresence: (optional) use information about whether or not chirality is specified when computing rank [default=False]
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::vector<int,class std::allocator<int> > CanonicalRankAtomsInFragment(class RDKit::ROMol,class boost::python::api::object [,class boost::python::api::object=0 [,class boost::python::api::object=0 [,bool=True [,bool=True [,bool=True [,bool=True [,bool=False]]]]]]])
    """
def CanonicalizeEnhancedStereo(mol: Mol) -> None:
    """
        C++ signature :
            void CanonicalizeEnhancedStereo(class RDKit::ROMol {lvalue})
    """
def CreateAtomBoolPropertyList(mol: Mol, propName: str, missingValueMarker: str = '', lineSize: int = 190) -> None:
    """
        creates a list property on the molecule from individual atom property values
    
        C++ signature :
            void CreateAtomBoolPropertyList(class RDKit::ROMol {lvalue},class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='' [,unsigned int=190]])
    """
def CreateAtomDoublePropertyList(mol: Mol, propName: str, missingValueMarker: str = '', lineSize: int = 190) -> None:
    """
        creates a list property on the molecule from individual atom property values
    
        C++ signature :
            void CreateAtomDoublePropertyList(class RDKit::ROMol {lvalue},class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='' [,unsigned int=190]])
    """
def CreateAtomIntPropertyList(mol: Mol, propName: str, missingValueMarker: str = '', lineSize: int = 190) -> None:
    """
        creates a list property on the molecule from individual atom property values
    
        C++ signature :
            void CreateAtomIntPropertyList(class RDKit::ROMol {lvalue},class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='' [,unsigned int=190]])
    """
def CreateAtomStringPropertyList(mol: Mol, propName: str, missingValueMarker: str = '', lineSize: int = 190) -> None:
    """
        creates a list property on the molecule from individual atom property values
    
        C++ signature :
            void CreateAtomStringPropertyList(class RDKit::ROMol {lvalue},class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='' [,unsigned int=190]])
    """
def MetadataFromPNGFile(filename: typing.Any, asList: bool = False) -> typing.Any:
    """
        Returns a dict with all metadata from the PNG file. Keys are strings, values are bytes. If asList is True, a list of (key, value) tuples is returned; this enables retrieving multiple values sharing the same key.
    
        C++ signature :
            struct _object * __ptr64 MetadataFromPNGFile(class boost::python::api::object [,bool=False])
    """
def MetadataFromPNGString(png: typing.Any, asList: bool = False) -> typing.Any:
    """
        Returns a dict with all metadata from the PNG string. Keys are strings, values are bytes. If asList is True, a list of (key, value) tuples is returned; this enables retrieving multiple values sharing the same key.
    
        C++ signature :
            struct _object * __ptr64 MetadataFromPNGString(class boost::python::api::object [,bool=False])
    """
def MolFragmentToCXSmarts(mol: Mol, atomsToUse: typing.Any, bondsToUse: typing.Any = 0, isomericSmarts: bool = True) -> str:
    """
        Returns a SMARTS string for a fragment of a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - atomsToUse: indices of atoms to include in the SMARTS string
            - bondsToUse: indices of bonds to include in the SMARTS string (optional)
            - isomericSmarts: (optional) include information about stereochemistry in
              the SMARTS.  Defaults to true.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolFragmentToCXSmarts(class RDKit::ROMol,class boost::python::api::object [,class boost::python::api::object=0 [,bool=True]])
    """
@typing.overload
def MolFragmentToCXSmiles(mol: Mol, params: SmilesWriteParams, atomsToUse: typing.Any, bondsToUse: typing.Any = 0, atomSymbols: typing.Any = 0, bondSymbols: typing.Any = 0) -> str:
    """
        Returns the CXSMILES string for a fragment of a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - params: the SmilesWriteParams 
            - atomsToUse : a list of atoms to include in the fragment
            - bondsToUse : (optional) a list of bonds to include in the fragment
              if not provided, all bonds between the atoms provided
              will be included.
            - atomSymbols : (optional) a list with the symbols to use for the atoms
              in the SMILES. This should have be mol.GetNumAtoms() long.
            - bondSymbols : (optional) a list with the symbols to use for the bonds
              in the SMILES. This should have be mol.GetNumBonds() long.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolFragmentToCXSmiles(class RDKit::ROMol,struct RDKit::SmilesWriteParams,class boost::python::api::object [,class boost::python::api::object=0 [,class boost::python::api::object=0 [,class boost::python::api::object=0]]])
    """
@typing.overload
def MolFragmentToCXSmiles(mol: Mol, atomsToUse: typing.Any, bondsToUse: typing.Any = 0, atomSymbols: typing.Any = 0, bondSymbols: typing.Any = 0, isomericSmiles: bool = True, kekuleSmiles: bool = False, rootedAtAtom: int = -1, canonical: bool = True, allBondsExplicit: bool = False, allHsExplicit: bool = False) -> str:
    """
        Returns the CXSMILES string for a fragment of a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - atomsToUse : a list of atoms to include in the fragment
            - bondsToUse : (optional) a list of bonds to include in the fragment
              if not provided, all bonds between the atoms provided
              will be included.
            - atomSymbols : (optional) a list with the symbols to use for the atoms
              in the SMILES. This should have be mol.GetNumAtoms() long.
            - bondSymbols : (optional) a list with the symbols to use for the bonds
              in the SMILES. This should have be mol.GetNumBonds() long.
            - isomericSmiles: (optional) include information about stereochemistry in
              the SMILES.  Defaults to true.
            - kekuleSmiles: (optional) use the Kekule form (no aromatic bonds) in
              the SMILES.  Defaults to false.
            - rootedAtAtom: (optional) if non-negative, this forces the SMILES 
              to start at a particular atom. Defaults to -1.  If not -1, overrides
              canonical setting.
            - canonical: (optional) if false no attempt will be made to canonicalize
              the molecule. Defaults to true.
            - allBondsExplicit: (optional) if true, all bond orders will be explicitly indicated
              in the output SMILES. Defaults to false.
            - allHsExplicit: (optional) if true, all H counts will be explicitly indicated
              in the output SMILES. Defaults to false.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolFragmentToCXSmiles(class RDKit::ROMol,class boost::python::api::object [,class boost::python::api::object=0 [,class boost::python::api::object=0 [,class boost::python::api::object=0 [,bool=True [,bool=False [,int=-1 [,bool=True [,bool=False [,bool=False]]]]]]]]])
    """
def MolFragmentToSmarts(mol: Mol, atomsToUse: typing.Any, bondsToUse: typing.Any = 0, isomericSmarts: bool = True) -> str:
    """
        Returns a SMARTS string for a fragment of a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - atomsToUse: indices of atoms to include in the SMARTS string
            - bondsToUse: indices of bonds to include in the SMARTS string (optional)
            - isomericSmarts: (optional) include information about stereochemistry in
              the SMARTS.  Defaults to true.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolFragmentToSmarts(class RDKit::ROMol,class boost::python::api::object [,class boost::python::api::object=0 [,bool=True]])
    """
@typing.overload
def MolFragmentToSmiles(mol: Mol, params: SmilesWriteParams, atomsToUse: typing.Any, bondsToUse: typing.Any = 0, atomSymbols: typing.Any = 0, bondSymbols: typing.Any = 0) -> str:
    """
        Returns the canonical SMILES string for a fragment of a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - params: the SmilesWriteParams 
            - atomsToUse : a list of atoms to include in the fragment
            - bondsToUse : (optional) a list of bonds to include in the fragment
              if not provided, all bonds between the atoms provided
              will be included.
            - atomSymbols : (optional) a list with the symbols to use for the atoms
              in the SMILES. This should have be mol.GetNumAtoms() long.
            - bondSymbols : (optional) a list with the symbols to use for the bonds
              in the SMILES. This should have be mol.GetNumBonds() long.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolFragmentToSmiles(class RDKit::ROMol,struct RDKit::SmilesWriteParams,class boost::python::api::object [,class boost::python::api::object=0 [,class boost::python::api::object=0 [,class boost::python::api::object=0]]])
    """
@typing.overload
def MolFragmentToSmiles(mol: Mol, atomsToUse: typing.Any, bondsToUse: typing.Any = 0, atomSymbols: typing.Any = 0, bondSymbols: typing.Any = 0, isomericSmiles: bool = True, kekuleSmiles: bool = False, rootedAtAtom: int = -1, canonical: bool = True, allBondsExplicit: bool = False, allHsExplicit: bool = False) -> str:
    """
        Returns the canonical SMILES string for a fragment of a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - atomsToUse : a list of atoms to include in the fragment
            - bondsToUse : (optional) a list of bonds to include in the fragment
              if not provided, all bonds between the atoms provided
              will be included.
            - atomSymbols : (optional) a list with the symbols to use for the atoms
              in the SMILES. This should have be mol.GetNumAtoms() long.
            - bondSymbols : (optional) a list with the symbols to use for the bonds
              in the SMILES. This should have be mol.GetNumBonds() long.
            - isomericSmiles: (optional) include information about stereochemistry in
              the SMILES.  Defaults to true.
            - kekuleSmiles: (optional) use the Kekule form (no aromatic bonds) in
              the SMILES.  Defaults to false.
            - rootedAtAtom: (optional) if non-negative, this forces the SMILES 
              to start at a particular atom. Defaults to -1.  If not -1, over-rides
              setting for canonical.
            - canonical: (optional) if false no attempt will be made to canonicalize
              the molecule. Defaults to true.
            - allBondsExplicit: (optional) if true, all bond orders will be explicitly indicated
              in the output SMILES. Defaults to false.
            - allHsExplicit: (optional) if true, all H counts will be explicitly indicated
              in the output SMILES. Defaults to false.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolFragmentToSmiles(class RDKit::ROMol,class boost::python::api::object [,class boost::python::api::object=0 [,class boost::python::api::object=0 [,class boost::python::api::object=0 [,bool=True [,bool=False [,int=-1 [,bool=True [,bool=False [,bool=False]]]]]]]]])
    """
def MolFromFASTA(text: typing.Any, sanitize: bool = True, flavor: int = 0) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a FASTA string (currently supports standard amino acids, DNA and RNA bases).
        
          ARGUMENTS:
        
            - text: string containing the FASTA
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
        - flavor: (optional)
            - 0 Protein, L amino acids (default)
            - 1 Protein, D amino acids
            - 2 RNA, no cap
            - 3 RNA, 5' cap
            - 4 RNA, 3' cap
            - 5 RNA, both caps
            - 6 DNA, no cap
            - 7 DNA, 5' cap
            - 8 DNA, 3' cap
            - 9 DNA, both caps
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromFASTA(class boost::python::api::object [,bool=True [,int=0]])
    """
def MolFromHELM(text: typing.Any, sanitize: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a HELM string (currently supports standard amino acids, DNA and RNA bases).
        
          ARGUMENTS:
        
            - text: string containing the HELM
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromHELM(class boost::python::api::object [,bool=True])
    """
def MolFromMol2Block(mol2Block: str, sanitize: bool = True, removeHs: bool = True, cleanupSubstructures: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a Tripos Mol2 block.
        
          NOTE:
            The parser expects the atom-typing scheme used by Corina.
            Atom types from Tripos' dbtranslate are less supported.
            Other atom typing schemes are unlikely to work.
        
          ARGUMENTS:
        
            - mol2Block: string containing the Mol2 block
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
            - cleanupSubstructures: (optional) toggles standardizing some 
              substructures found in mol2 files.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromMol2Block(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,bool=True]]])
    """
def MolFromMol2File(mol2FileName: str, sanitize: bool = True, removeHs: bool = True, cleanupSubstructures: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a Tripos Mol2 file.
        
          NOTE:
            The parser expects the atom-typing scheme used by Corina.
            Atom types from Tripos' dbtranslate are less supported.
            Other atom typing schemes are unlikely to work.
        
          ARGUMENTS:
                                          
            - mol2FileName: name of the file to read
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to true.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
            - cleanupSubstructures: (optional) toggles standardizing some 
              substructures found in mol2 files.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromMol2File(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,bool=True]]])
    """
@typing.overload
def MolFromMolBlock(molBlock: typing.Any, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a Mol block.
        
          ARGUMENTS:
        
            - molBlock: string containing the Mol block
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
            - strictParsing: (optional) if this is false, the parser is more lax about.
              correctness of the content.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromMolBlock(class boost::python::api::object [,bool=True [,bool=True [,bool=True]]])
    """
@typing.overload
def MolFromMolBlock(molBlock: typing.Any, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a Mol block.
        
          ARGUMENTS:
        
            - molBlock: string containing the Mol block
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
            - strictParsing: (optional) if this is false, the parser is more lax about.
              correctness of the content.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromMolBlock(class boost::python::api::object [,bool=True [,bool=True [,bool=True]]])
    """
@typing.overload
def MolFromMolFile(molFileName: str, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a Mol file.
        
          ARGUMENTS:
        
            - fileName: name of the file to read
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to true.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
            - strictParsing: (optional) if this is false, the parser is more lax about.
              correctness of the content.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromMolFile(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,bool=True]]])
    """
@typing.overload
def MolFromMolFile(molFileName: str, sanitize: bool = True, removeHs: bool = True, strictParsing: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a Mol file.
        
          ARGUMENTS:
        
            - molFileName: name of the file to read
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to true.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
            - strictParsing: (optional) if this is false, the parser is more lax about.
              correctness of the content.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromMolFile(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,bool=True]]])
    """
def MolFromMrvBlock(mrvBlock: typing.Any, sanitize: bool = True, removeHs: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a Marvin (mrv) block.
        
          ARGUMENTS:
        
            - molBlock: string containing the Marvin block
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromMrvBlock(class boost::python::api::object [,bool=True [,bool=True]])
    """
def MolFromMrvFile(molFileName: str, sanitize: bool = True, removeHs: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a Marvin (Mrv) file.
        
          ARGUMENTS:
        
            - fileName: name of the file to read
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to true.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromMrvFile(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True]])
    """
def MolFromPDBBlock(molBlock: typing.Any, sanitize: bool = True, removeHs: bool = True, flavor: int = 0, proximityBonding: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a PDB block.
        
          ARGUMENTS:
        
            - molBlock: string containing the PDB block
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
            - flavor: (optional) 
        
            - proximityBonding: (optional) toggles automatic proximity bonding
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromPDBBlock(class boost::python::api::object [,bool=True [,bool=True [,unsigned int=0 [,bool=True]]]])
    """
def MolFromPDBFile(pdbFileName: str, sanitize: bool = True, removeHs: bool = True, flavor: int = 0, proximityBonding: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a PDB file.
        
          ARGUMENTS:
        
            - pdbFileName: name of the file to read
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to true.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
            - flavor: (optional) 
        
            - proximityBonding: (optional) toggles automatic proximity bonding
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromPDBFile(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,unsigned int=0 [,bool=True]]]])
    """
def MolFromPNGFile(filename: str, params: typing.Any = None) -> rdkit.Chem.Mol:
    """
        Construct a molecule from metadata in a PNG file.
        
             ARGUMENTS:
        
               - filename: the PNG filename
        
               - params: used to provide optional parameters for the metadata parsing
        
             RETURNS:
               a Mol object, None on failure.
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromPNGFile(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class boost::python::api::object=None])
    """
def MolFromPNGString(png: typing.Any, params: typing.Any = None) -> rdkit.Chem.Mol:
    """
        Construct a molecule from metadata in a PNG string.
        
             ARGUMENTS:
        
               - png: the PNG string
        
               - params: used to provide optional parameters for the metadata parsing
        
             RETURNS:
               a Mol object, None on failure.
          
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromPNGString(class boost::python::api::object [,class boost::python::api::object=None])
    """
def MolFromRDKitSVG(svg: typing.Any, sanitize: bool = True, removeHs: bool = True) -> rdkit.Chem.Mol:
    """
        Construct a molecule from an RDKit-generate SVG string.
        
          ARGUMENTS:
        
            - svg: string containing the SVG data (must include molecule metadata)
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - removeHs: (optional) toggles removing hydrogens from the molecule.
              This only make sense when sanitization is done.
              Defaults to true.
        
          RETURNS:
        
            a Mol object, None on failure.
        
          NOTE: this functionality should be considered beta.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromRDKitSVG(class boost::python::api::object [,bool=True [,bool=True]])
    """
def MolFromSCSRBlock(molBlock: str, sanitize: bool = True, removeHs: bool = True, molFromSCSRParams: typing.Any = None) -> rdkit.Chem.Mol:
    """
        Construct a molecule from an SCSR Mol block.
        
              ARGUMENTS:
            
                - molBlock: string containing the SCSR Mol block
            
                - sanitize: (optional) toggles sanitization of the molecule.
                  Defaults to True.
            
                - removeHs: (optional) toggles removing hydrogens from the molecule.
                  This only make sense when sanitization is done.
                  Defaults to true.
            
                - molFromSCSRParams : MolFromSCSRParams to control conversion
            
         RETURNS :
            
         a Mol object, None on failure.
            
         
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromSCSRBlock(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,class boost::python::api::object=None]]])
    """
def MolFromSCSRFile(filename: str, sanitize: bool = True, removeHs: bool = True, molFromSCSRParams: typing.Any = None) -> rdkit.Chem.Mol:
    """
        Construct a molecule from an SCSR Mol block.
        
              ARGUMENTS:
            
                - filename: string containing the SCSR filename
            
                - sanitize: (optional) toggles sanitization of the molecule.
                  Defaults to True.
            
                - removeHs: (optional) toggles removing hydrogens from the molecule.
                  This only make sense when sanitization is done.
                  Defaults to true.
            
                - molFromSCSRParams : MolFromSCSRParams to control conversion
            
         RETURNS :
            
         a Mol object, None on failure.
            
         
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromSCSRFile(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True [,class boost::python::api::object=None]]])
    """
def MolFromSequence(text: typing.Any, sanitize: bool = True, flavor: int = 0) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a sequence string (currently supports standard amino acids, DNA and RNA bases).
        
          ARGUMENTS:
        
            - text: string containing the sequence
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - flavor: (optional)
                - 0 Protein, L amino acids (default)
                - 1 Protein, D amino acids
                - 2 RNA, no cap
                - 3 RNA, 5' cap
                - 4 RNA, 3' cap
                - 5 RNA, both caps
                - 6 DNA, no cap
                - 7 DNA, 5' cap
                - 8 DNA, 3' cap
                - 9 DNA, both caps
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromSequence(class boost::python::api::object [,bool=True [,int=0]])
    """
@typing.overload
def MolFromSmarts(SMARTS: typing.Any, mergeHs: bool = False, replacements: dict = {}) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a SMARTS string.
        
          ARGUMENTS:
        
            - SMARTS: the smarts string
        
            - mergeHs: (optional) toggles the merging of explicit Hs in the query into the attached
              atoms.  So, for example, 'C[H]' becomes '[C;!H0]'.
              Defaults to 0.
        
            - replacements: (optional) a dictionary of replacement strings (see below)
              Defaults to {}. See the documentation for MolFromSmiles for an explanation.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromSmarts(class boost::python::api::object [,bool=False [,class boost::python::dict={}]])
    """
@typing.overload
def MolFromSmarts(SMARTS: typing.Any, params: SmartsParserParams) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a SMARTS string.
        
             ARGUMENTS:
        
               - SMARTS: the smarts string
        
               - params: used to provide optional parameters for the SMARTS parsing
        
             RETURNS:
        
               a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromSmarts(class boost::python::api::object,struct RDKit::v1::SmartsParserParams)
    """
@typing.overload
def MolFromSmiles(SMILES: typing.Any, params: SmilesParserParams) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a SMILES string.
        
             ARGUMENTS:
        
               - SMILES: the smiles string
        
               - params: used to provide optional parameters for the SMILES parsing
        
             RETURNS:
        
               a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromSmiles(class boost::python::api::object,struct RDKit::v1::SmilesParserParams)
    """
@typing.overload
def MolFromSmiles(SMILES: typing.Any, sanitize: bool = True, replacements: dict = {}) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a SMILES string.
        
          ARGUMENTS:
        
            - SMILES: the smiles string
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - replacements: (optional) a dictionary of replacement strings (see below)
              Defaults to {}.
        
          RETURNS:
        
            a Mol object, None on failure.
        
           The optional replacements dict can be used to do string substitution of abbreviations 
           in the input SMILES. The set of substitutions is repeatedly looped through until 
           the string no longer changes. It is the responsibility of the caller to make sure 
           that substitutions results in legal and sensible SMILES. 
        
           Examples of replacements: 
        
             CC{Q}C with {'{Q}':'OCCO'} -> CCOCCOC  
        
             C{A}C{Q}C with {'{Q}':'OCCO', '{A}':'C1(CC1)'} -> CC1(CC1)COCCOC  
        
             C{A}C{Q}C with {'{Q}':'{X}CC{X}', '{A}':'C1CC1', '{X}':'N'} -> CC1CC1CNCCNC  
        
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromSmiles(class boost::python::api::object [,bool=True [,class boost::python::dict={}]])
    """
def MolFromTPLBlock(tplBlock: typing.Any, sanitize: bool = True, skipFirstConf: bool = False) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a TPL block.
        
          ARGUMENTS:
        
            - tplBlock: string containing the TPL block
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - skipFirstConf: (optional) skips reading the first conformer.
              Defaults to False.
              This should be set to True when reading TPLs written by 
              the CombiCode.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromTPLBlock(class boost::python::api::object [,bool=True [,bool=False]])
    """
def MolFromTPLFile(fileName: str, sanitize: bool = True, skipFirstConf: bool = False) -> rdkit.Chem.Mol:
    """
        Construct a molecule from a TPL file.
        
          ARGUMENTS:
        
            - fileName: name of the file to read
        
            - sanitize: (optional) toggles sanitization of the molecule.
              Defaults to True.
        
            - skipFirstConf: (optional) skips reading the first conformer.
              Defaults to False.
              This should be set to True when reading TPLs written by 
              the CombiCode.
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromTPLFile(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=False]])
    """
def MolFromXYZBlock(xyzBlock: typing.Any) -> rdkit.Chem.Mol:
    """
        Construct a molecule from an XYZ string.
        
          ARGUMENTS:
        
            - xyzBlock: the XYZ data to read
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromXYZBlock(class boost::python::api::object)
    """
def MolFromXYZFile(xyzFileName: str) -> rdkit.Chem.Mol:
    """
        Construct a molecule from an XYZ file.
        
          ARGUMENTS:
        
            - xyzFileName: name of the file to read
        
          RETURNS:
        
            a Mol object, None on failure.
        
        
    
        C++ signature :
            class RDKit::ROMol * __ptr64 MolFromXYZFile(char const * __ptr64)
    """
@typing.overload
def MolMetadataToPNGFile(mol: Mol, filename: typing.Any, includePkl: bool = True, includeSmiles: bool = True, includeMol: bool = False) -> typing.Any:
    """
        Adds molecular metadata to PNG data read from a file.
        
             ARGUMENTS:
        
               - mol: the molecule
        
               - filename: the PNG filename
        
               - includePkl: include the RDKit's internal binary format in the output
        
               - includeSmiles: include CXSmiles in the output
        
               - includeMol: include CTAB (Mol) in the output
        
             RETURNS:
               the updated PNG data
    
        C++ signature :
            class boost::python::api::object MolMetadataToPNGFile(class RDKit::ROMol,class boost::python::api::object [,bool=True [,bool=True [,bool=False]]])
    """
@typing.overload
def MolMetadataToPNGFile(mol: Mol, filename: typing.Any, params: PNGMetadataParams) -> typing.Any:
    """
        Adds molecular metadata to PNG data read from a file.
        
             ARGUMENTS:
        
               - mol: the molecule
        
               - filename: the PNG filename
        
               - params: an instance of PNGMetadataParams
        
             RETURNS:
               the updated PNG data
    
        C++ signature :
            class boost::python::api::object MolMetadataToPNGFile(class RDKit::ROMol,class boost::python::api::object,struct RDKit::PNGMetadataParams)
    """
@typing.overload
def MolMetadataToPNGString(mol: Mol, png: typing.Any, includePkl: bool = True, includeSmiles: bool = True, includeMol: bool = False) -> typing.Any:
    """
        Adds molecular metadata to a PNG string.
        
             ARGUMENTS:
        
               - mol: the molecule
        
               - png: the PNG string
        
               - includePkl: include the RDKit's internal binary format in the output
        
               - includeSmiles: include CXSmiles in the output
        
               - includeMol: include CTAB (Mol) in the output
        
             RETURNS:
               the updated PNG data
    
        C++ signature :
            class boost::python::api::object MolMetadataToPNGString(class RDKit::ROMol,class boost::python::api::object [,bool=True [,bool=True [,bool=False]]])
    """
@typing.overload
def MolMetadataToPNGString(mol: Mol, png: typing.Any, params: PNGMetadataParams) -> typing.Any:
    """
        Adds molecular metadata to a PNG string.
        
             ARGUMENTS:
        
               - mol: the molecule
        
               - png: the PNG string
        
               - params: an instance of PNGMetadataParams
        
             RETURNS:
               the updated PNG data
    
        C++ signature :
            class boost::python::api::object MolMetadataToPNGString(class RDKit::ROMol,class boost::python::api::object,struct RDKit::PNGMetadataParams)
    """
def MolToCMLBlock(mol: Mol, confId: int = -1, kekulize: bool = True) -> str:
    """
        Writes a CML block for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - confId: (optional) selects which conformation to output
            - kekulize: (optional) triggers kekulization of the molecule before it's written
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToCMLBlock(class RDKit::ROMol [,int=-1 [,bool=True]])
    """
def MolToCMLFile(mol: Mol, filename: str, confId: int = -1, kekulize: bool = True) -> None:
    """
        Writes a CML file for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - filename: the file to write to
            - confId: (optional) selects which conformation to output
            - kekulize: (optional) triggers kekulization of the molecule before it's written
        
        
    
        C++ signature :
            void MolToCMLFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,int=-1 [,bool=True]])
    """
def MolToCXSmarts(mol: Mol, isomericSmiles: bool = True) -> str:
    """
        Returns a SMARTS string for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - isomericSmiles: (optional) include information about stereochemistry in
              the SMARTS.  Defaults to true.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToCXSmarts(class RDKit::ROMol [,bool=True])
    """
@typing.overload
def MolToCXSmiles(mol: Mol, params: SmilesWriteParams, flags: int = ..., restoreBondDirs: RestoreBondDirOption = ...) -> str:
    """
        Returns the CXSMILES string for a molecule
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToCXSmiles(class RDKit::ROMol,struct RDKit::SmilesWriteParams [,unsigned int=rdkit.Chem.rdmolfiles.CXSmilesFields.CX_ALL [,enum RDKit::RestoreBondDirOption=rdkit.Chem.rdmolfiles.RestoreBondDirOption.RestoreBondDirOptionClear]])
    """
@typing.overload
def MolToCXSmiles(mol: Mol, isomericSmiles: bool = True, kekuleSmiles: bool = False, rootedAtAtom: int = -1, canonical: bool = True, allBondsExplicit: bool = False, allHsExplicit: bool = False, doRandom: bool = False) -> str:
    """
        Returns the CXSMILES string for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - isomericSmiles: (optional) include information about stereochemistry in
              the SMILES.  Defaults to true.
            - kekuleSmiles: (optional) use the Kekule form (no aromatic bonds) in
              the SMILES.  Defaults to false.
            - rootedAtAtom: (optional) if non-negative, this forces the SMILES 
              to start at a particular atom. Defaults to -1.
            - canonical: (optional) if false no attempt will be made to canonicalize
              the molecule. Defaults to true.
            - allBondsExplicit: (optional) if true, all bond orders will be explicitly indicated
              in the output SMILES. Defaults to false.
            - allHsExplicit: (optional) if true, all H counts will be explicitly indicated
              in the output SMILES. Defaults to false.
            - doRandom: (optional) if true, randomizes the traversal of the molecule graph,
              so we can generate random smiles. Defaults to false.  If true, overrides
              canonical setting.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToCXSmiles(class RDKit::ROMol [,bool=True [,bool=False [,int=-1 [,bool=True [,bool=False [,bool=False [,bool=False]]]]]]])
    """
def MolToFASTA(mol: Mol) -> str:
    """
        Returns the FASTA string for a molecule
          ARGUMENTS:
        
            - mol: the molecule
        
          NOTE: the molecule should contain monomer information in AtomMonomerInfo structures 
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToFASTA(class RDKit::ROMol)
    """
def MolToHELM(mol: Mol) -> str:
    """
        Returns the HELM string for a molecule
          ARGUMENTS:
        
            - mol: the molecule
        
          NOTE: the molecule should contain monomer information in AtomMonomerInfo structures 
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToHELM(class RDKit::ROMol)
    """
@typing.overload
def MolToMolBlock(mol: Mol, params: MolWriterParams, confId: int = -1) -> str:
    """
        Returns a Mol block for a molecule
          Arguments:
            - mol: the molecule
            - params: the MolWriterParams
            - confId: (optional) selects which conformation to output (-1 = default)
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToMolBlock(class RDKit::ROMol,struct RDKit::MolWriterParams [,int=-1])
    """
@typing.overload
def MolToMolBlock(mol: Mol, includeStereo: bool = True, confId: int = -1, kekulize: bool = True, forceV3000: bool = False) -> str:
    """
        Returns a Mol block for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - includeStereo: (optional) toggles inclusion of stereochemical
              information in the output
            - confId: (optional) selects which conformation to output (-1 = default)
            - kekulize: (optional) triggers kekulization of the molecule before it's written,
              as suggested by the MDL spec.
            - forceV3000 (optional) force generation a V3000 mol block (happens automatically with 
              more than 999 atoms or bonds)
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToMolBlock(class RDKit::ROMol [,bool=True [,int=-1 [,bool=True [,bool=False]]]])
    """
@typing.overload
def MolToMolFile(mol: Mol, filename: str, params: MolWriterParams, confId: int = -1) -> None:
    """
        Writes a Mol file for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - filename: the file to write to
            - params: the MolWriterParams
            - confId: (optional) selects which conformation to output (-1 = default)
        
        
    
        C++ signature :
            void MolToMolFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,struct RDKit::MolWriterParams [,int=-1])
    """
@typing.overload
def MolToMolFile(mol: Mol, filename: str, includeStereo: bool = True, confId: int = -1, kekulize: bool = True, forceV3000: bool = False) -> None:
    """
        Writes a Mol file for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - filename: the file to write to
            - includeStereo: (optional) toggles inclusion of stereochemical
              information in the output
            - confId: (optional) selects which conformation to output (-1 = default)
            - kekulize: (optional) triggers kekulization of the molecule before it's written,
              as suggested by the MDL spec.
            - forceV3000 (optional) force generation a V3000 mol block (happens automatically with 
              more than 999 atoms or bonds)
        
        
    
        C++ signature :
            void MolToMolFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,int=-1 [,bool=True [,bool=False]]]])
    """
@typing.overload
def MolToMrvBlock(mol: Mol, includeStereo: bool = True, confId: int = -1, kekulize: bool = True, prettyPrint: bool = False) -> str:
    """
        Returns a Marvin (Mrv) Mol block for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - includeStereo: (optional) toggles inclusion of stereochemical
              information in the output
            - confId: (optional) selects which conformation to output (-1 = default)
            - kekulize: (optional) triggers kekulization of the molecule before it's written.
            - prettyPrint: (optional) makes the output more human readable.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToMrvBlock(class RDKit::ROMol [,bool=True [,int=-1 [,bool=True [,bool=False]]]])
    """
@typing.overload
def MolToMrvBlock(mol: Mol, params: typing.Any, confId: int = -1) -> str:
    """
        Returns a Marvin (Mrv) Mol block for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - params: marvin write params
            - confId: (optional) selects which conformation to output (-1 = default)
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToMrvBlock(class RDKit::ROMol,struct RDKit::MrvWriterParams [,int=-1])
    """
@typing.overload
def MolToMrvFile(mol: Mol, filename: str, includeStereo: bool = True, confId: int = -1, kekulize: bool = True, prettyPrint: bool = False) -> None:
    """
        Writes a Marvin (MRV) file for a molecule
           ARGUMENTS:
        
             - mol: the molecule
             - filename: the file to write to
             - includeStereo: (optional) toggles inclusion of stereochemical
               information in the output
             - confId: (optional) selects which conformation to output (-1 = default)
             - kekulize: (optional) triggers kekulization of the molecule before it's written.
             - prettyPrint: (optional) makes the output more human readable.
        
        
    
        C++ signature :
            void MolToMrvFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,int=-1 [,bool=True [,bool=False]]]])
    """
@typing.overload
def MolToMrvFile(mol: Mol, filename: str, params: typing.Any, confId: int = -1) -> None:
    """
        Writes a Marvin (MRV) file for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - filename: the file to write to
            - params: marvin write params
            - confId: (optional) selects which conformation to output (-1 = default)
        
        
    
        C++ signature :
            void MolToMrvFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >,struct RDKit::MrvWriterParams [,int=-1])
    """
def MolToPDBBlock(mol: Mol, confId: int = -1, flavor: int = 0) -> str:
    """
        Returns a PDB block for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - confId: (optional) selects which conformation to output (-1 = default)
            - flavor: (optional) 
                    - flavor & 1 : Write MODEL/ENDMDL lines around each record 
                    - flavor & 2 : Don't write any CONECT records 
                    - flavor & 4 : Write CONECT records in both directions 
                    - flavor & 8 : Don't use multiple CONECTs to encode bond order 
                    - flavor & 16 : Write MASTER record 
                    - flavor & 32 : Write TER record 
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToPDBBlock(class RDKit::ROMol [,int=-1 [,unsigned int=0]])
    """
def MolToPDBFile(mol: Mol, filename: str, confId: int = -1, flavor: int = 0) -> None:
    """
        Writes a PDB file for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - filename: name of the file to write
            - confId: (optional) selects which conformation to output (-1 = default)
            - flavor: (optional) 
                    - flavor & 1 : Write MODEL/ENDMDL lines around each record 
                    - flavor & 2 : Don't write any CONECT records 
                    - flavor & 4 : Write CONECT records in both directions 
                    - flavor & 8 : Don't use multiple CONECTs to encode bond order 
                    - flavor & 16 : Write MASTER record 
                    - flavor & 32 : Write TER record 
        
        
    
        C++ signature :
            void MolToPDBFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,int=-1 [,unsigned int=0]])
    """
def MolToRandomSmilesVect(mol: Mol, numSmiles: int, randomSeed: int = 0, isomericSmiles: bool = True, kekuleSmiles: bool = False, allBondsExplicit: bool = False, allHsExplicit: bool = False) -> list:
    """
        returns a list of SMILES generated using the randomSmiles algorithm
    
        C++ signature :
            class boost::python::list MolToRandomSmilesVect(class RDKit::ROMol,unsigned int [,unsigned int=0 [,bool=True [,bool=False [,bool=False [,bool=False]]]]])
    """
def MolToSequence(mol: Mol) -> str:
    """
        Returns the sequence string for a molecule
          ARGUMENTS:
        
            - mol: the molecule
        
          NOTE: the molecule should contain monomer information in AtomMonomerInfo structures 
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToSequence(class RDKit::ROMol)
    """
@typing.overload
def MolToSmarts(mol: Mol, isomericSmiles: bool = True, rootedAtAtom: int = -1) -> str:
    """
        Returns a SMARTS string for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - isomericSmiles: (optional) include information about stereochemistry in
              the SMARTS.  Defaults to true.
            - rootedAtomAtom: (optional) the atom index to start the SMARTS from.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToSmarts(class RDKit::ROMol [,bool=True [,int=-1]])
    """
@typing.overload
def MolToSmarts(mol: Mol, params: SmilesWriteParams) -> str:
    """
        Returns a SMARTS string for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - params: SmilesWriteParams controlling the SMARTS generation
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToSmarts(class RDKit::ROMol,struct RDKit::SmilesWriteParams)
    """
@typing.overload
def MolToSmiles(mol: Mol, params: SmilesWriteParams) -> str:
    """
        Returns the canonical SMILES string for a molecule
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToSmiles(class RDKit::ROMol,struct RDKit::SmilesWriteParams)
    """
@typing.overload
def MolToSmiles(mol: Mol, isomericSmiles: bool = True, kekuleSmiles: bool = False, rootedAtAtom: int = -1, canonical: bool = True, allBondsExplicit: bool = False, allHsExplicit: bool = False, doRandom: bool = False, ignoreAtomMapNumbers: bool = False) -> str:
    """
        Returns the canonical SMILES string for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - isomericSmiles: (optional) include information about stereochemistry in
              the SMILES.  Defaults to true.
            - kekuleSmiles: (optional) use the Kekule form (no aromatic bonds) in
              the SMILES.  Defaults to false.
            - rootedAtAtom: (optional) if non-negative, this forces the SMILES 
              to start at a particular atom. Defaults to -1.  If not -1, overrides
              canonical setting.
            - canonical: (optional) if false no attempt will be made to canonicalize
              the molecule. Defaults to true.
            - allBondsExplicit: (optional) if true, all bond orders will be explicitly indicated
              in the output SMILES. Defaults to false.
            - allHsExplicit: (optional) if true, all H counts will be explicitly indicated
              in the output SMILES. Defaults to false.
            - doRandom: (optional) if true, randomize the traversal of the molecule graph,
              so we can generate random smiles. Defaults to false.  If true, overrides
              canonical setting.
            - ignoreAtomMapNumbers (optional) if true, ignores any atom map numbers when
              canonicalizing the molecule 
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToSmiles(class RDKit::ROMol [,bool=True [,bool=False [,int=-1 [,bool=True [,bool=False [,bool=False [,bool=False [,bool=False]]]]]]]])
    """
def MolToTPLBlock(mol: Mol, partialChargeProp: str = '_GasteigerCharge', writeFirstConfTwice: bool = False) -> str:
    """
        Returns the Tpl block for a molecule.
        
          ARGUMENTS:
        
            - mol: the molecule
            - partialChargeProp: name of the property to use for partial charges
              Defaults to '_GasteigerCharge'.
            - writeFirstConfTwice: Defaults to False.
              This should be set to True when writing TPLs to be read by 
              the CombiCode.
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToTPLBlock(class RDKit::ROMol [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='_GasteigerCharge' [,bool=False]])
    """
def MolToTPLFile(mol: Mol, fileName: str, partialChargeProp: str = '_GasteigerCharge', writeFirstConfTwice: bool = False) -> None:
    """
        Writes a molecule to a TPL file.
        
          ARGUMENTS:
        
            - mol: the molecule
            - fileName: name of the file to write
            - partialChargeProp: name of the property to use for partial charges
              Defaults to '_GasteigerCharge'.
            - writeFirstConfTwice: Defaults to False.
              This should be set to True when writing TPLs to be read by 
              the CombiCode.
        
        
    
        C++ signature :
            void MolToTPLFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='_GasteigerCharge' [,bool=False]])
    """
def MolToV2KMolBlock(mol: Mol, params: typing.Any = None, confId: int = -1) -> str:
    """
        Returns a V2000 Mol block for a molecule
           ARGUMENTS:
        
             - mol: the molecule
             - params: the MolWriterParams
             - confId: (optional) selects which conformation to output (-1 = default)
        
           RETURNS:
        
             a string
        
           NOTE: this function throws a ValueError if the molecule has more than 999 atoms, bonds, or SGroups
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToV2KMolBlock(class RDKit::ROMol [,class boost::python::api::object=None [,int=-1]])
    """
@typing.overload
def MolToV3KMolBlock(mol: Mol, params: MolWriterParams, confId: int = -1) -> str:
    """
        Returns a V3000 Mol block for a molecule
           ARGUMENTS:
        
              - mol: the molecule
             - params: the MolWriterParams
             - confId: (optional) selects which conformation to output (-1 = default)
        
            RETURNS:
        
              a string
        
         
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToV3KMolBlock(class RDKit::ROMol,struct RDKit::MolWriterParams [,int=-1])
    """
@typing.overload
def MolToV3KMolBlock(mol: Mol, includeStereo: bool = True, confId: int = -1, kekulize: bool = True) -> str:
    """
        Returns a V3000 Mol block for a molecule
           ARGUMENTS:
        
              - mol: the molecule
             - includeStereo: (optional) toggles inclusion of stereochemical
               information in the output
             - confId: (optional) selects which conformation to output (-1 = default)
             - kekulize: (optional) triggers kekulization of the molecule before it's written,
               as suggested by the MDL spec.
        
            RETURNS:
        
              a string
        
         
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToV3KMolBlock(class RDKit::ROMol [,bool=True [,int=-1 [,bool=True]]])
    """
@typing.overload
def MolToV3KMolFile(mol: Mol, filename: str, params: MolWriterParams = True, confId: int = -1) -> None:
    """
        Writes a V3000 Mol file for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - filename: the file to write to
            - params: the MolWriterParams
            - confId: (optional) selects which conformation to output (-1 = default)
        
        
    
        C++ signature :
            void MolToV3KMolFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,struct RDKit::MolWriterParams=True [,int=-1]])
    """
@typing.overload
def MolToV3KMolFile(mol: Mol, filename: str, includeStereo: bool = True, confId: int = -1, kekulize: bool = True) -> None:
    """
        Writes a V3000 Mol file for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - filename: the file to write to
            - includeStereo: (optional) toggles inclusion of stereochemical
              information in the output
            - confId: (optional) selects which conformation to output (-1 = default)
            - kekulize: (optional) triggers kekulization of the molecule before it's written,
              as suggested by the MDL spec.
        
        
    
        C++ signature :
            void MolToV3KMolFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,int=-1 [,bool=True]]])
    """
def MolToXYZBlock(mol: Mol, confId: int = -1, precision: int = 6) -> str:
    """
        Returns a XYZ block for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - confId: (optional) selects which conformation to output (-1 = default)
            - precision: precision of the coordinates
        
          RETURNS:
        
            a string
        
        
    
        C++ signature :
            class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > MolToXYZBlock(class RDKit::ROMol [,int=-1 [,unsigned int=6]])
    """
def MolToXYZFile(mol: Mol, filename: str, confId: int = -1, precision: int = 6) -> None:
    """
        Writes a XYZ file for a molecule
          ARGUMENTS:
        
            - mol: the molecule
            - filename: the file to write to
            - confId: (optional) selects which conformation to output (-1 = default)
            - precision: precision of the coordinates
        
        
    
        C++ signature :
            void MolToXYZFile(class RDKit::ROMol,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,int=-1 [,unsigned int=6]])
    """
def MolsFromCDXML(cdxml: typing.Any, sanitize: bool = True, removeHs: bool = True) -> tuple:
    """
        Construct a molecule from a cdxml string.
        
             Note that the CDXML format is large and complex, the RDKit doesn't support
             full functionality, just the base ones required for molecule and
             reaction parsing.
        
             ARGUMENTS:
        
               - cdxml: the cdxml string
        
               - sanitize: if True, sanitize the molecules [default True]
        
               - removeHs: if True, convert explicit Hs into implicit Hs. [default True]
        
             RETURNS:
               an iterator of parsed Mol objects.
    
        C++ signature :
            class boost::python::tuple MolsFromCDXML(class boost::python::api::object [,bool=True [,bool=True]])
    """
def MolsFromCDXMLFile(filename: str, sanitize: bool = True, removeHs: bool = True) -> typing.Any:
    """
        Construct a molecule from a cdxml file.
        
             Note that the CDXML format is large and complex, the RDKit doesn't support
             full functionality, just the base ones required for molecule and
             reaction parsing.
        
             ARGUMENTS:
        
               - filename: the cdxml filename
        
               - sanitize: if True, sanitize the molecules [default True]
        
               - removeHs: if True, convert explicit Hs into implicit Hs. [default True]
        
             RETURNS:
               an iterator of parsed Mol objects.
    
        C++ signature :
            class boost::python::api::object MolsFromCDXMLFile(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,bool=True [,bool=True]])
    """
def MolsFromPNGFile(filename: str, tag: str = 'rdkitPKL', params: typing.Any = None) -> typing.Any:
    """
        returns a tuple of molecules constructed from the PNG file
    
        C++ signature :
            class boost::python::api::object MolsFromPNGFile(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='rdkitPKL' [,class boost::python::api::object=None]])
    """
def MolsFromPNGString(png: typing.Any, tag: str = 'rdkitPKL', params: typing.Any = None) -> tuple:
    """
        returns a tuple of molecules constructed from the PNG string
    
        C++ signature :
            class boost::python::tuple MolsFromPNGString(class boost::python::api::object [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >='rdkitPKL' [,class boost::python::api::object=None]])
    """
def SmilesMolSupplierFromText(text: str, delimiter: str = '', smilesColumn: int = 0, nameColumn: int = 1, titleLine: bool = True, sanitize: bool = True) -> SmilesMolSupplier:
    """
        C++ signature :
            class RDKit::v1::SmilesMolSupplier * __ptr64 SmilesMolSupplierFromText(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > [,class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> >=' ' [,int=0 [,int=1 [,bool=True [,bool=True]]]]])
    """
