from __future__ import annotations
import _io
from _io import StringIO
import os as os
from rdkit import Chem
from rdkit import RDConfig
import rdkit.VLib.Filter
from rdkit.VLib import Filter
from rdkit.VLib.NodeLib import SDSupply
from rdkit.VLib.NodeLib import SmartsMolFilter
import rdkit.VLib.NodeLib.SmartsMolFilter
import rdkit.VLib.NodeLib.SmartsRemover
from rdkit.VLib.NodeLib import SmartsRemover
from rdkit.VLib.NodeLib import SmilesDupeFilter
import rdkit.VLib.NodeLib.SmilesDupeFilter
import rdkit.VLib.NodeLib.SmilesOutput
from rdkit.VLib.NodeLib import SmilesOutput
import rdkit.VLib.Supply
from rdkit.VLib import Supply
__all__ = ['Chem', 'Filter', 'RDConfig', 'SDSupply', 'SmartsMolFilter', 'SmartsRemover', 'SmilesDupeFilter', 'SmilesOutput', 'StringIO', 'Supply', 'atsFilter', 'dupeFilter', 'i', 'io', 'metals', 'mols', 'os', 'output', 'remover', 'salts', 'smaFilter', 'smis', 'supplier']
atsFilter: rdkit.VLib.Filter.FilterNode  # value = <rdkit.VLib.Filter.FilterNode object>
dupeFilter: rdkit.VLib.NodeLib.SmilesDupeFilter.DupeFilter  # value = <rdkit.VLib.NodeLib.SmilesDupeFilter.DupeFilter object>
i: int = 6
io: _io.StringIO  # value = <_io.StringIO object>
metals: str = '[#21,#22,#23,#24,#25,#26,#27,#28,#29,#39,#40,#41,#42,#43,#44,#45,#46,#47,#57,#58,#59,#60,#61,#62,#63,#64,#65,#66,#67,#68,#69,#70,#71,#72,#73,#74,#75,#76,#77,#78,#79]'
mols: list  # value = [<rdkit.Chem.rdchem.Mol object>, <rdkit.Chem.rdchem.Mol object>, <rdkit.Chem.rdchem.Mol object>, <rdkit.Chem.rdchem.Mol object>, <rdkit.Chem.rdchem.Mol object>, <rdkit.Chem.rdchem.Mol object>, <rdkit.Chem.rdchem.Mol object>]
output: rdkit.VLib.NodeLib.SmilesOutput.OutputNode  # value = <rdkit.VLib.NodeLib.SmilesOutput.OutputNode object>
remover: rdkit.VLib.NodeLib.SmartsRemover.SmartsRemover  # value = <rdkit.VLib.NodeLib.SmartsRemover.SmartsRemover object>
salts: list = ['[Cl;H1&X1,-]', '[Na+]', '[O;H2,H1&-,X0&-2]']
smaFilter: rdkit.VLib.NodeLib.SmartsMolFilter.SmartsFilter  # value = <rdkit.VLib.NodeLib.SmartsMolFilter.SmartsFilter object>
smis: list = ['CCOC', 'CCO.Cl', 'CC(=O)[O-].[Na+]', 'CC[Cu]CC', 'OCC', 'C[N+](C)(C)C.[Cl-]', '[Na+].[Cl-]']
supplier: rdkit.VLib.Supply.SupplyNode  # value = <rdkit.VLib.Supply.SupplyNode object>
