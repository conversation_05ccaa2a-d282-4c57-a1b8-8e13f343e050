#!/usr/bin/env python3
"""
Run chemprop-IR/predict.py with convenient defaults and plot predicted spectra.

Example (defaults use the bundled experiment_model and limit to 25 molecules):
    python run_predict_and_plot.py

Customize paths or limits:
    python run_predict_and_plot.py \
        --test_path trained_ir_model/experiment_model/test_smiles.csv \
        --features_path trained_ir_model/experiment_model/test_features.csv \
        --checkpoint_dir trained_ir_model/experiment_model \
        --preds_path trained_ir_model/experiment_model/preds_smoke.csv \
        --max_data_size 25 \
        --plots_dir trained_ir_model/experiment_model/plots_smoke \
        --save_pdf
"""
from __future__ import annotations

import argparse
import csv
import os
import subprocess
import sys
from pathlib import Path
from typing import List

# Use non-interactive backend
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
from rdkit import Chem



def parse_args() -> argparse.Namespace:
    cwd = Path.cwd()
    default_test = cwd / "trained_ir_model/experiment_model/test_smiles.csv"
    default_feats = cwd / "trained_ir_model/experiment_model/test_features.csv"
    default_ckpt_dir = cwd / "trained_ir_model/experiment_model"
    default_preds = cwd / "trained_ir_model/experiment_model/preds_smoke.csv"
    default_plots = cwd / "trained_ir_model/experiment_model/plots_smoke"

    p = argparse.ArgumentParser(description="Run chemprop-IR prediction and plot spectra")
    p.add_argument("--test_path", type=Path, default=default_test)
    p.add_argument("--features_path", type=Path, default=default_feats)
    p.add_argument("--checkpoint_dir", type=Path, default=default_ckpt_dir)
    p.add_argument("--preds_path", type=Path, default=default_preds)
    p.add_argument("--max_data_size", type=int, default=25, help="Limit number of molecules for quick runs")
    p.add_argument("--plots_dir", type=Path, default=default_plots, help="Directory to save per-molecule PNGs")
    p.add_argument("--dpi", type=int, default=150)
    p.add_argument("--limit_plots", type=int, default=None, help="Optionally limit number of plots saved")
    p.add_argument("--save_pdf", action="store_true", help="Also save a combined PDF of all plots")
    p.add_argument("--title_with_smiles", action="store_true", help="Use SMILES string on plot title")
    p.add_argument("--skip_invalid", action="store_true", help="Skip plotting rows with all-empty predictions")
    return p.parse_args()


def run_predict_script(test_path: Path, features_path: Path, checkpoint_dir: Path, preds_path: Path, max_data_size: int | None) -> None:
    script_path = Path(__file__).resolve().parents[1] / "predict.py"
    cmd: List[str] = [
        sys.executable,
        str(script_path),
        "--test_path", str(test_path),
        "--features_path", str(features_path),
        "--checkpoint_dir", str(checkpoint_dir),
        "--preds_path", str(preds_path),
    ]
    if max_data_size is not None:
        cmd += ["--max_data_size", str(max_data_size)]

    print("Running:")
    print(" ", " ".join(cmd))
    subprocess.run(cmd, check=True)


def read_preds(preds_path: Path):
    with open(preds_path, "r", newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames or []
        if not headers:
            raise RuntimeError("No headers found in predictions CSV")
        # Identify frequency columns: those headers convertible to float
        freq_cols: List[str] = []
        for h in headers:
            if h.lower() == "smiles" or h.lower() == "compound_names":
                continue
            try:
                float(h)
                freq_cols.append(h)
            except ValueError:
                # skip non-numeric headers (e.g., epi_unc)
                pass
        if not freq_cols:
            raise RuntimeError("No numeric frequency columns found in predictions CSV")
        freqs = [float(h) for h in freq_cols]
        rows = list(reader)
        return freqs, freq_cols, rows
def write_invalid_report(preds_path: Path, report_path: Path) -> int:
    """Scan prediction CSV and write invalid/empty rows with SMILES and RDKit check.
    Returns the count of invalid rows.
    """
    invalid = []
    with open(preds_path, "r", newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames or []
        # numeric frequency columns
        freq_cols: List[str] = []
        for h in headers:
            if h.lower() in ("smiles", "compound_names"):
                continue
            try:
                float(h); freq_cols.append(h)
            except ValueError:
                pass
        for row in reader:
            # a row is considered invalid if all frequency cells are empty
            empties = [row.get(c, "") == "" for c in freq_cols]
            if all(empties):
                smi = row.get("smiles", "")
                can_parse = Chem.MolFromSmiles(smi) is not None if smi else False
                invalid.append((smi, can_parse))
    report_path.parent.mkdir(parents=True, exist_ok=True)
    with open(report_path, "w", encoding="utf-8") as out:
        out.write("smiles\tRDKit_parse_ok\n")
        for smi, ok in invalid:
            out.write(f"{smi}\t{ok}\n")
    return len(invalid)



def sanitize_filename(s: str, maxlen: int = 60) -> str:
    bad = '<>:"/\\|?*'
    clean = ''.join((c if c not in bad else '_') for c in s)
    return (clean[:maxlen]).strip('_') or "mol"


def plot_spectra(freqs: List[float], freq_cols: List[str], rows: List[dict], plots_dir: Path, dpi: int, limit_plots: int | None, save_pdf: bool, title_with_smiles: bool, skip_invalid: bool) -> None:
    plots_dir.mkdir(parents=True, exist_ok=True)
    pdf_path = plots_dir / "spectra_plots.pdf"

    pdf = None
    if save_pdf:
        from matplotlib.backends.backend_pdf import PdfPages
        pdf = PdfPages(str(pdf_path))

    count = 0
    for idx, row in enumerate(rows):
        if limit_plots is not None and count >= limit_plots:
            break
        smiles = row.get("smiles", f"index_{idx}")
        y_vals = []
        for col in freq_cols:
            val = row.get(col, '')
            try:
                y_vals.append(float(val) if val != '' else float('nan'))
            except ValueError:
                y_vals.append(float('nan'))
        # Optionally skip rows with all NaN (all-empty predictions)
        if skip_invalid:
            all_nan = True
            for y in y_vals:
                if y == y:  # NaN != NaN, so this detects non-NaN
                    all_nan = False
                    break
            if all_nan:
                continue

        fig, ax = plt.subplots(figsize=(8, 4))
        ax.plot(freqs, y_vals, lw=1.2)
        ax.set_xlabel("Wavenumber (cm^-1)")
        ax.set_ylabel("Predicted intensity")
        # IR spectra are often shown high->low wavenumber; allow user flip if needed.
        ax.invert_xaxis()
        if title_with_smiles:
            ax.set_title(smiles)
        ax.grid(True, ls=':', alpha=0.4)
        fig.tight_layout()

        fname = f"{idx:04d}_" + sanitize_filename(smiles) + ".png"
        out_path = plots_dir / fname
        fig.savefig(out_path, dpi=dpi)
        if pdf is not None:
            pdf.savefig(fig)
        plt.close(fig)

        count += 1

    if pdf is not None:
        pdf.close()
        print(f"Saved combined PDF: {pdf_path}")

    print(f"Saved {count} plot(s) to: {plots_dir}")


def main():
    args = parse_args()

    # 1) Run prediction
    run_predict_script(
        test_path=args.test_path,
        features_path=args.features_path,
        checkpoint_dir=args.checkpoint_dir,
        preds_path=args.preds_path,
        max_data_size=args.max_data_size,
    )

    # 2) Read predictions and plot
    freqs, freq_cols, rows = read_preds(args.preds_path)

    # 2.1) Write invalid rows report
    report_path = args.plots_dir / "invalid_predictions.tsv"
    invalid_count = write_invalid_report(args.preds_path, report_path)
    if invalid_count:
        print(f"Found {invalid_count} invalid rows. Details: {report_path}")

    # 2.2) Plot spectra
    plot_spectra(
        freqs=freqs,
        freq_cols=freq_cols,
        rows=rows,
        plots_dir=args.plots_dir,
        dpi=args.dpi,
        limit_plots=args.limit_plots,
        save_pdf=args.save_pdf,
        title_with_smiles=args.title_with_smiles,
        skip_invalid=args.skip_invalid,
    )


if __name__ == "__main__":
    main()

