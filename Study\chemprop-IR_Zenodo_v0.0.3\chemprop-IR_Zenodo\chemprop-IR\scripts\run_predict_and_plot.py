#!/usr/bin/env python3
"""
Run chemprop-IR/predict.py with convenient defaults and plot predicted spectra.

Example (defaults use the bundled experiment_model and limit to 25 molecules):
    python run_predict_and_plot.py

Customize paths or limits:
    python run_predict_and_plot.py \
        --test_path trained_ir_model/experiment_model/test_smiles.csv \
        --features_path trained_ir_model/experiment_model/test_features.csv \
        --checkpoint_dir trained_ir_model/experiment_model \
        --preds_path trained_ir_model/experiment_model/preds_smoke.csv \
        --max_data_size 25 \
        --plots_dir trained_ir_model/experiment_model/plots_smoke \
        --save_pdf
"""
from __future__ import annotations

import argparse
import csv
import os
import subprocess
import sys
from pathlib import Path
from typing import List, Dict, Tuple
from collections import defaultdict

# Use non-interactive backend
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
from rdkit import Chem
import numpy as np



def parse_args() -> argparse.Namespace:
    cwd = Path.cwd()
    default_test = cwd / "trained_ir_model/experiment_model/test_smiles.csv"
    default_feats = cwd / "trained_ir_model/experiment_model/test_features.csv"
    default_ckpt_dir = cwd / "trained_ir_model/experiment_model"
    default_preds = cwd / "trained_ir_model/experiment_model/preds_smoke.csv"
    default_plots = cwd / "trained_ir_model/experiment_model/plots_smoke"

    p = argparse.ArgumentParser(description="Run chemprop-IR prediction and plot spectra")
    p.add_argument("--test_path", type=Path, default=default_test)
    p.add_argument("--features_path", type=Path, default=default_feats)
    p.add_argument("--checkpoint_dir", type=Path, default=default_ckpt_dir)
    p.add_argument("--preds_path", type=Path, default=default_preds)
    p.add_argument("--max_data_size", type=int, default=25, help="Limit number of molecules for quick runs")
    p.add_argument("--plots_dir", type=Path, default=default_plots, help="Directory to save per-molecule PNGs")
    p.add_argument("--dpi", type=int, default=150)
    p.add_argument("--limit_plots", type=int, default=None, help="Optionally limit number of plots saved")
    p.add_argument("--save_pdf", action="store_true", help="Also save a combined PDF of all plots")
    p.add_argument("--title_with_smiles", action="store_true", help="Use SMILES string on plot title")
    p.add_argument("--skip_invalid", action="store_true", help="Skip plotting rows with all-empty predictions")
    p.add_argument("--use_best_data", action="store_true", default=True, help="Use only the best quality data for each molecule (default: True)")
    p.add_argument("--nan_threshold", type=float, default=0.05, help="Maximum fraction of NaN values allowed (default: 0.05)")
    return p.parse_args()


def run_predict_script(test_path: Path, features_path: Path, checkpoint_dir: Path, preds_path: Path, max_data_size: int | None) -> None:
    script_path = Path(__file__).resolve().parents[1] / "predict.py"
    cmd: List[str] = [
        sys.executable,
        str(script_path),
        "--test_path", str(test_path),
        "--features_path", str(features_path),
        "--checkpoint_dir", str(checkpoint_dir),
        "--preds_path", str(preds_path),
    ]
    if max_data_size is not None:
        cmd += ["--max_data_size", str(max_data_size)]

    print("Running:")
    print(" ", " ".join(cmd))
    subprocess.run(cmd, check=True)


def read_preds(preds_path: Path):
    with open(preds_path, "r", newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames or []
        if not headers:
            raise RuntimeError("No headers found in predictions CSV")
        # Identify frequency columns: those headers convertible to float
        freq_cols: List[str] = []
        for h in headers:
            if h.lower() == "smiles" or h.lower() == "compound_names":
                continue
            try:
                float(h)
                freq_cols.append(h)
            except ValueError:
                # skip non-numeric headers (e.g., epi_unc)
                pass
        if not freq_cols:
            raise RuntimeError("No numeric frequency columns found in predictions CSV")
        freqs = [float(h) for h in freq_cols]
        rows = list(reader)
        return freqs, freq_cols, rows
def write_invalid_report(preds_path: Path, report_path: Path) -> int:
    """Scan prediction CSV and write invalid/empty rows with SMILES and RDKit check.
    Returns the count of invalid rows.
    """
    invalid = []
    with open(preds_path, "r", newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames or []
        # numeric frequency columns
        freq_cols: List[str] = []
        for h in headers:
            if h.lower() in ("smiles", "compound_names"):
                continue
            try:
                float(h); freq_cols.append(h)
            except ValueError:
                pass
        for row in reader:
            # a row is considered invalid if all frequency cells are empty
            empties = [row.get(c, "") == "" for c in freq_cols]
            if all(empties):
                smi = row.get("smiles", "")
                can_parse = Chem.MolFromSmiles(smi) is not None if smi else False
                invalid.append((smi, can_parse))
    report_path.parent.mkdir(parents=True, exist_ok=True)
    with open(report_path, "w", encoding="utf-8") as out:
        out.write("smiles\tRDKit_parse_ok\n")
        for smi, ok in invalid:
            out.write(f"{smi}\t{ok}\n")
    return len(invalid)



def evaluate_data_quality(y_vals: List[float]) -> Dict[str, float]:
    """评估数据质量，返回质量指标"""
    y_array = np.array(y_vals)
    total_points = len(y_array)
    nan_count = np.isnan(y_array).sum()
    valid_count = total_points - nan_count

    quality_metrics = {
        'nan_fraction': nan_count / total_points if total_points > 0 else 1.0,
        'valid_count': valid_count,
        'total_count': total_points,
        'completeness_score': valid_count / total_points if total_points > 0 else 0.0
    }

    # 如果有有效数据，计算额外的质量指标
    if valid_count > 0:
        valid_data = y_array[~np.isnan(y_array)]
        quality_metrics.update({
            'mean_intensity': np.mean(valid_data),
            'std_intensity': np.std(valid_data),
            'max_intensity': np.max(valid_data),
            'min_intensity': np.min(valid_data)
        })

    return quality_metrics


def select_best_data_per_molecule(rows: List[dict], freq_cols: List[str], nan_threshold: float = 0.05) -> Tuple[List[dict], Dict[str, Dict]]:
    """为每个分子选择最佳质量的数据"""
    molecule_data = defaultdict(list)

    # 按分子分组并评估数据质量
    for idx, row in enumerate(rows):
        smiles = row.get("smiles", f"index_{idx}")

        # 提取数值数据
        y_vals = []
        for col in freq_cols:
            val = row.get(col, '')
            try:
                y_vals.append(float(val) if val != '' else float('nan'))
            except ValueError:
                y_vals.append(float('nan'))

        # 评估数据质量
        quality = evaluate_data_quality(y_vals)

        molecule_data[smiles].append({
            'row_index': idx,
            'row_data': row,
            'y_vals': y_vals,
            'quality': quality
        })

    # 为每个分子选择最佳数据
    selected_rows = []
    quality_report = {}

    for smiles, candidates in molecule_data.items():
        # 按数据质量排序：优先选择完整度高、NaN少的数据
        candidates.sort(key=lambda x: (
            -x['quality']['completeness_score'],  # 完整度高的优先
            x['quality']['nan_fraction'],          # NaN比例低的优先
            -x['quality']['valid_count']           # 有效数据点多的优先
        ))

        best_candidate = candidates[0]

        # 检查最佳候选是否满足质量要求
        if best_candidate['quality']['nan_fraction'] <= nan_threshold:
            selected_rows.append(best_candidate['row_data'])
            quality_report[smiles] = {
                'selected_row_index': best_candidate['row_index'],
                'total_candidates': len(candidates),
                'quality_metrics': best_candidate['quality'],
                'status': 'selected'
            }
        else:
            # 如果最佳候选也不满足要求，记录但不选择
            quality_report[smiles] = {
                'selected_row_index': None,
                'total_candidates': len(candidates),
                'quality_metrics': best_candidate['quality'],
                'status': 'rejected_poor_quality'
            }

    return selected_rows, quality_report


def write_quality_report(quality_report: Dict[str, Dict], report_path: Path) -> None:
    """写入数据质量报告"""
    report_path.parent.mkdir(parents=True, exist_ok=True)

    with open(report_path, "w", encoding="utf-8") as f:
        f.write("smiles\tstatus\tselected_row_index\ttotal_candidates\tnan_fraction\tcompleteness_score\tvalid_count\ttotal_count\n")

        for smiles, info in quality_report.items():
            metrics = info['quality_metrics']
            f.write(f"{smiles}\t{info['status']}\t{info['selected_row_index']}\t{info['total_candidates']}\t"
                   f"{metrics['nan_fraction']:.4f}\t{metrics['completeness_score']:.4f}\t"
                   f"{metrics['valid_count']}\t{metrics['total_count']}\n")


def sanitize_filename(s: str, maxlen: int = 60) -> str:
    bad = '<>:"/\\|?*'
    clean = ''.join((c if c not in bad else '_') for c in s)
    return (clean[:maxlen]).strip('_') or "mol"


def plot_spectra(freqs: List[float], freq_cols: List[str], rows: List[dict], plots_dir: Path, dpi: int, limit_plots: int | None, save_pdf: bool, title_with_smiles: bool, skip_invalid: bool, use_best_data: bool = True, nan_threshold: float = 0.05) -> None:
    plots_dir.mkdir(parents=True, exist_ok=True)
    pdf_path = plots_dir / "spectra_plots.pdf"

    # 数据预处理：选择最佳质量数据
    if use_best_data:
        print("正在选择每个分子的最佳质量数据...")
        selected_rows, quality_report = select_best_data_per_molecule(rows, freq_cols, nan_threshold)

        # 写入质量报告
        quality_report_path = plots_dir / "data_quality_report.tsv"
        write_quality_report(quality_report, quality_report_path)

        # 统计信息
        total_molecules = len(quality_report)
        selected_molecules = len(selected_rows)
        rejected_molecules = total_molecules - selected_molecules

        print(f"数据质量分析完成:")
        print(f"  总分子数: {total_molecules}")
        print(f"  选择的分子数: {selected_molecules}")
        print(f"  拒绝的分子数: {rejected_molecules} (数据质量不佳)")
        print(f"  质量报告已保存: {quality_report_path}")

        # 使用选择的数据
        rows_to_plot = selected_rows
    else:
        # 使用原始数据
        rows_to_plot = rows

    pdf = None
    if save_pdf:
        from matplotlib.backends.backend_pdf import PdfPages
        pdf = PdfPages(str(pdf_path))

    count = 0
    plotted_molecules = set()  # 跟踪已绘制的分子，避免重复

    for idx, row in enumerate(rows_to_plot):
        if limit_plots is not None and count >= limit_plots:
            break

        smiles = row.get("smiles", f"index_{idx}")

        # 如果不使用最佳数据选择，手动去重
        if not use_best_data and smiles in plotted_molecules:
            continue
        plotted_molecules.add(smiles)

        y_vals = []
        for col in freq_cols:
            val = row.get(col, '')
            try:
                y_vals.append(float(val) if val != '' else float('nan'))
            except ValueError:
                y_vals.append(float('nan'))

        # 数据质量检查
        quality = evaluate_data_quality(y_vals)

        # 可选择跳过无效数据
        if skip_invalid and quality['nan_fraction'] > nan_threshold:
            print(f"跳过分子 {smiles}: NaN比例 {quality['nan_fraction']:.3f} > 阈值 {nan_threshold}")
            continue

        # 绘制光谱图
        fig, ax = plt.subplots(figsize=(10, 6))

        # 处理NaN值：只绘制有效数据点
        y_array = np.array(y_vals)
        freq_array = np.array(freqs)
        valid_mask = ~np.isnan(y_array)

        if np.sum(valid_mask) > 0:
            ax.plot(freq_array[valid_mask], y_array[valid_mask], lw=1.5, color='blue')

            # 如果有缺失数据，标记缺失区域
            if quality['nan_fraction'] > 0:
                nan_indices = np.where(np.isnan(y_array))[0]
                if len(nan_indices) > 0:
                    # 找到连续的NaN区域
                    consecutive_groups = []
                    start = nan_indices[0]
                    for i in range(1, len(nan_indices)):
                        if nan_indices[i] != nan_indices[i-1] + 1:
                            consecutive_groups.append((start, nan_indices[i-1]))
                            start = nan_indices[i]
                    consecutive_groups.append((start, nan_indices[-1]))

                    # 标记缺失区域
                    for start_idx, end_idx in consecutive_groups:
                        freq_start = freqs[start_idx]
                        freq_end = freqs[end_idx]
                        ax.axvspan(freq_end, freq_start, alpha=0.2, color='red',
                                 label=f'缺失数据区域' if start_idx == consecutive_groups[0][0] else "")

        ax.set_xlabel("Wavenumber (cm⁻¹)", fontsize=12)
        ax.set_ylabel("Predicted Intensity", fontsize=12)
        ax.invert_xaxis()  # IR光谱通常从高频到低频显示

        # 设置标题
        if title_with_smiles:
            title = f"{smiles}"
            if quality['nan_fraction'] > 0:
                title += f"\n(完整度: {quality['completeness_score']:.1%})"
            ax.set_title(title, fontsize=10)

        ax.grid(True, ls=':', alpha=0.4)

        # 如果有缺失数据，添加图例
        if quality['nan_fraction'] > 0:
            ax.legend(loc='upper right', fontsize=8)

        fig.tight_layout()

        # 保存文件
        fname = f"{count:04d}_" + sanitize_filename(smiles) + ".png"
        out_path = plots_dir / fname
        fig.savefig(out_path, dpi=dpi, bbox_inches='tight')

        if pdf is not None:
            pdf.savefig(fig, bbox_inches='tight')

        plt.close(fig)
        count += 1

    if pdf is not None:
        pdf.close()
        print(f"已保存合并PDF: {pdf_path}")

    print(f"已保存 {count} 个光谱图到: {plots_dir}")


def main():
    args = parse_args()

    # 1) Run prediction
    run_predict_script(
        test_path=args.test_path,
        features_path=args.features_path,
        checkpoint_dir=args.checkpoint_dir,
        preds_path=args.preds_path,
        max_data_size=args.max_data_size,
    )

    # 2) Read predictions and plot
    freqs, freq_cols, rows = read_preds(args.preds_path)

    # 2.1) Write invalid rows report
    report_path = args.plots_dir / "invalid_predictions.tsv"
    invalid_count = write_invalid_report(args.preds_path, report_path)
    if invalid_count:
        print(f"Found {invalid_count} invalid rows. Details: {report_path}")

    # 2.2) Plot spectra
    plot_spectra(
        freqs=freqs,
        freq_cols=freq_cols,
        rows=rows,
        plots_dir=args.plots_dir,
        dpi=args.dpi,
        limit_plots=args.limit_plots,
        save_pdf=args.save_pdf,
        title_with_smiles=args.title_with_smiles,
        skip_invalid=args.skip_invalid,
        use_best_data=args.use_best_data,
        nan_threshold=args.nan_threshold,
    )


if __name__ == "__main__":
    main()

