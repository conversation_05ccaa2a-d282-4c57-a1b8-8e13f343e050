#!/usr/bin/env python3

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import csv

def reproduce_original_plot():
    """完全复制原始脚本的绘图逻辑"""
    
    # 使用与原始脚本相同的参数
    csv_path = 'trained_ir_model/experiment_model/preds_smoke.csv'
    target_smiles = 'CC(C)(C)C1=CC(=O)C=C(C(C)(C)C)C1=O'
    
    # 生成频率轴（与原始脚本相同）
    freqs = np.arange(400, 4002, 2)
    print(f'频率轴: {len(freqs)} 个点，范围 {freqs[0]}-{freqs[-1]} cm^-1')
    
    # 读取CSV文件（使用原始脚本的方法）
    with open(csv_path, 'r', newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        freq_cols = [col for col in reader.fieldnames if col != 'smiles']
        
        print(f'CSV频率列数: {len(freq_cols)}')
        
        for idx, row in enumerate(reader):
            smiles = row.get("smiles", f"index_{idx}")
            
            if smiles != target_smiles:
                continue
                
            print(f'找到目标分子: {smiles}')
            
            # 完全复制原始脚本的数据处理逻辑
            y_vals = []
            for col in freq_cols:
                val = row.get(col, '')
                try:
                    y_vals.append(float(val) if val != '' else float('nan'))
                except ValueError:
                    y_vals.append(float('nan'))
            
            print(f'处理后的数据点数: {len(y_vals)}')
            
            # 检查NaN值
            y_vals_array = np.array(y_vals)
            nan_count = np.isnan(y_vals_array).sum()
            print(f'NaN值数量: {nan_count}')
            
            if nan_count > 0:
                nan_indices = np.where(np.isnan(y_vals_array))[0]
                print(f'NaN位置: {nan_indices[:20]}...' if len(nan_indices) > 20 else f'NaN位置: {nan_indices}')
                
                # 找到连续的NaN区域
                if len(nan_indices) > 1:
                    consecutive_groups = []
                    start = nan_indices[0]
                    for i in range(1, len(nan_indices)):
                        if nan_indices[i] != nan_indices[i-1] + 1:
                            consecutive_groups.append((start, nan_indices[i-1]))
                            start = nan_indices[i]
                    consecutive_groups.append((start, nan_indices[-1]))
                    
                    print(f'连续NaN区域数量: {len(consecutive_groups)}')
                    for i, (start, end) in enumerate(consecutive_groups[:5]):
                        print(f'  区域{i+1}: 位置 {start}-{end} (长度: {end-start+1})')
                        # 对应的频率范围
                        freq_start = freqs[start] if start < len(freqs) else 'N/A'
                        freq_end = freqs[end] if end < len(freqs) else 'N/A'
                        print(f'    频率范围: {freq_start}-{freq_end} cm^-1')
            
            # 数据统计
            valid_vals = y_vals_array[~np.isnan(y_vals_array)]
            if len(valid_vals) > 0:
                print(f'有效数据统计:')
                print(f'  最小值: {np.min(valid_vals):.6f}')
                print(f'  最大值: {np.max(valid_vals):.6f}')
                print(f'  平均值: {np.mean(valid_vals):.6f}')
            
            # 完全复制原始脚本的绘图逻辑
            fig, ax = plt.subplots(figsize=(8, 4))
            ax.plot(freqs, y_vals, lw=1.2)
            ax.set_xlabel("Wavenumber (cm^-1)")
            ax.set_ylabel("Predicted intensity")
            ax.invert_xaxis()
            ax.set_title(smiles)
            ax.grid(True, ls=':', alpha=0.4)
            fig.tight_layout()
            
            # 保存图像
            output_file = 'reproduced_spectrum.png'
            fig.savefig(output_file, dpi=150)
            print(f'重现的图像已保存为: {output_file}')
            
            plt.close(fig)
            return

        print('未找到目标分子')

if __name__ == '__main__':
    reproduce_original_plot()
