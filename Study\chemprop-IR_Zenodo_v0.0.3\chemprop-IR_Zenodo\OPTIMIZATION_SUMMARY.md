# ChemProp-IR 脚本优化总结

## 优化目标
对 `chemprop-IR/scripts/run_predict_and_plot.py` 进行优化，解决以下问题：
1. 同一个分子生成多个谱图的问题
2. 某些谱图存在数据缺失（NaN值）的问题
3. 提高数据质量和可视化效果

## 问题分析

### 原始问题
- **集成预测产生重复数据**：10个模型的集成预测过程中，某些模型在特定频率区域预测失败，产生了多行数据
- **数据质量不一致**：同一分子的不同预测结果质量差异很大，有些包含大量NaN值
- **重复图像生成**：脚本为每行数据都生成图像，导致同一分子有多个谱图

### 具体案例
分子 `CC(C)(C)C1=CC(=O)C=C(C(C)(C)C)C1=O` 的原始数据：
- 第20行：完整度 100.0% (1801/1801 有效数据点)
- 第21行：完整度 90.3% (1627/1801 有效数据点，缺失2752-3098 cm⁻¹)
- 第22行：完整度 100.0% (1801/1801 有效数据点)

## 优化方案

### 1. 数据质量评估系统
```python
def evaluate_data_quality(y_vals: List[float]) -> Dict[str, float]:
    """评估数据质量，返回质量指标"""
    # 计算NaN比例、完整度、有效数据点数等指标
```

### 2. 智能数据选择算法
```python
def select_best_data_per_molecule(rows, freq_cols, nan_threshold=0.05):
    """为每个分子选择最佳质量的数据"""
    # 按分子分组，评估每行数据质量
    # 选择完整度最高、NaN最少的数据
```

### 3. 增强的可视化功能
- 自动标记数据缺失区域
- 显示数据完整度信息
- 改进图像质量和布局

### 4. 质量报告生成
生成详细的数据质量报告，包括：
- 每个分子的候选数据行数
- 选择的最佳数据行
- 数据完整度统计
- NaN值分布情况

## 新增参数

### 命令行参数
- `--use_best_data`: 启用最佳数据选择（默认：True）
- `--nan_threshold`: NaN值比例阈值（默认：0.05）

### 使用示例
```bash
# 使用优化后的脚本
python run_predict_and_plot.py --save_pdf --title_with_smiles --use_best_data --nan_threshold 0.05
```

## 优化效果

### 数据处理效果
- **原始数据**：25行预测数据，12个唯一分子，8个重复分子
- **优化后**：12个分子，每个分子1个高质量谱图
- **数据利用率**：100%（所有分子都有高质量数据）

### 图像生成效果
- **优化前**：25个图像文件（包含重复和低质量数据）
- **优化后**：12个图像文件（每个分子一个高质量谱图）
- **去重成功率**：100%

### 质量提升
- **平均完整度**：100%（所有选择的数据都是完整的）
- **平均NaN比例**：0%（成功避免了有缺失数据的预测结果）
- **特定分子处理**：目标分子选择了第20行（完整度100%），避免了第21行（完整度90.3%）

## 技术特性

### 数据质量指标
- `nan_fraction`: NaN值比例
- `completeness_score`: 数据完整度
- `valid_count`: 有效数据点数
- `total_count`: 总数据点数

### 选择策略
1. **优先级排序**：
   - 完整度高的优先
   - NaN比例低的优先
   - 有效数据点多的优先

2. **质量阈值**：
   - 默认NaN阈值：5%
   - 可通过参数调整

### 可视化增强
- 自动标记缺失数据区域（红色半透明区域）
- 在标题中显示数据完整度
- 改进的图像布局和质量
- 更好的坐标轴标签和网格

## 输出文件

### 生成的文件
1. **光谱图像**：`0000_分子名.png` - `0011_分子名.png`（12个文件）
2. **合并PDF**：`spectra_plots.pdf`
3. **质量报告**：`data_quality_report.tsv`
4. **无效预测报告**：`invalid_predictions.tsv`

### 质量报告格式
```
smiles	status	selected_row_index	total_candidates	nan_fraction	completeness_score	valid_count	total_count
分子SMILES	selected	选择的行号	候选行数	NaN比例	完整度	有效点数	总点数
```

## 使用建议

### 推荐设置
```bash
# 标准使用（推荐）
python run_predict_and_plot.py --save_pdf --title_with_smiles --use_best_data --nan_threshold 0.05

# 严格质量要求
python run_predict_and_plot.py --save_pdf --title_with_smiles --use_best_data --nan_threshold 0.01

# 宽松质量要求
python run_predict_and_plot.py --save_pdf --title_with_smiles --use_best_data --nan_threshold 0.1
```

### 参数说明
- `nan_threshold`: 控制数据质量要求，值越小要求越严格
- `use_best_data`: 建议始终启用，确保每个分子只有一个最佳谱图
- `save_pdf`: 生成合并的PDF文件，便于查看所有结果

## 总结

通过这次优化，成功解决了：
✅ **重复谱图问题**：每个分子现在只生成一个谱图
✅ **数据质量问题**：自动选择最高质量的数据
✅ **可视化问题**：改进了图像质量和信息展示
✅ **可追溯性**：提供详细的质量报告和选择依据

优化后的脚本不仅解决了原始问题，还提供了更好的用户体验和数据质量保证。
