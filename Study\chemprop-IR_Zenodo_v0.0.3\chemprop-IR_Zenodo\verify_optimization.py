#!/usr/bin/env python3

import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict

def verify_optimization():
    """验证脚本优化效果"""
    
    print("=== 脚本优化效果验证 ===\n")
    
    # 1. 读取原始预测数据
    preds_file = Path("trained_ir_model/experiment_model/preds_smoke.csv")
    df = pd.read_csv(preds_file)
    
    print(f"1. 原始预测数据分析:")
    print(f"   总行数: {len(df)}")
    print(f"   总列数: {len(df.columns)}")
    
    # 2. 分析分子重复情况
    smiles_counts = df['smiles'].value_counts()
    duplicated_smiles = smiles_counts[smiles_counts > 1]
    
    print(f"\n2. 分子重复情况:")
    print(f"   唯一分子数: {len(smiles_counts)}")
    print(f"   重复分子数: {len(duplicated_smiles)}")
    print(f"   总预测行数: {len(df)}")
    
    # 3. 读取质量报告
    quality_report_file = Path("trained_ir_model/experiment_model/plots_smoke/data_quality_report.tsv")
    if quality_report_file.exists():
        quality_df = pd.read_csv(quality_report_file, sep='\t')
        
        print(f"\n3. 数据质量报告分析:")
        print(f"   报告中的分子数: {len(quality_df)}")
        print(f"   选择的分子数: {len(quality_df[quality_df['status'] == 'selected'])}")
        print(f"   拒绝的分子数: {len(quality_df[quality_df['status'] != 'selected'])}")
        
        # 显示质量统计
        if len(quality_df) > 0:
            avg_completeness = quality_df['completeness_score'].mean()
            avg_nan_fraction = quality_df['nan_fraction'].mean()
            print(f"   平均完整度: {avg_completeness:.3f}")
            print(f"   平均NaN比例: {avg_nan_fraction:.3f}")
    
    # 4. 检查生成的图像文件
    plots_dir = Path("trained_ir_model/experiment_model/plots_smoke")
    png_files = list(plots_dir.glob("*.png"))
    
    print(f"\n4. 生成的图像文件:")
    print(f"   PNG文件总数: {len(png_files)}")
    
    # 分析文件名模式
    molecule_files = defaultdict(list)
    for png_file in png_files:
        # 提取分子名称（去掉序号前缀）
        filename = png_file.name
        if '_' in filename:
            parts = filename.split('_', 1)
            if len(parts) > 1:
                molecule_name = parts[1].replace('.png', '')
                molecule_files[molecule_name].append(filename)
    
    print(f"   唯一分子图像数: {len(molecule_files)}")
    
    # 检查是否还有重复
    duplicated_images = {mol: files for mol, files in molecule_files.items() if len(files) > 1}
    if duplicated_images:
        print(f"   仍有重复的分子图像: {len(duplicated_images)}")
        for mol, files in list(duplicated_images.items())[:3]:
            print(f"     {mol}: {len(files)} 个文件")
    else:
        print(f"   ✅ 没有重复的分子图像")
    
    # 5. 对比优化前后
    print(f"\n5. 优化效果对比:")
    print(f"   优化前:")
    print(f"     - 总预测行数: {len(df)}")
    print(f"     - 唯一分子数: {len(smiles_counts)}")
    print(f"     - 重复分子数: {len(duplicated_smiles)}")
    print(f"     - 平均每分子行数: {len(df) / len(smiles_counts):.1f}")
    
    if quality_report_file.exists():
        selected_count = len(quality_df[quality_df['status'] == 'selected'])
        print(f"   优化后:")
        print(f"     - 选择的分子数: {selected_count}")
        print(f"     - 生成的图像数: {len(molecule_files)}")
        print(f"     - 每分子图像数: 1 (去重成功)")
        print(f"     - 数据利用率: {selected_count / len(smiles_counts):.1%}")
    
    # 6. 检查特定分子的处理结果
    target_molecule = "CC(C)(C)C1=CC(=O)C=C(C(C)(C)C)C1=O"
    target_rows = df[df['smiles'] == target_molecule]
    
    print(f"\n6. 特定分子处理验证 (目标分子):")
    print(f"   分子: {target_molecule}")
    print(f"   原始数据行数: {len(target_rows)}")
    
    if len(target_rows) > 0:
        freq_cols = [col for col in df.columns if col != 'smiles']
        
        for idx, (row_idx, row) in enumerate(target_rows.iterrows()):
            values = pd.to_numeric(row[freq_cols], errors='coerce').values
            nan_count = np.isnan(values).sum()
            completeness = (len(values) - nan_count) / len(values)
            print(f"     行{row_idx}: 完整度 {completeness:.1%} ({len(values)-nan_count}/{len(values)})")
    
    # 检查是否在质量报告中
    if quality_report_file.exists():
        target_in_report = quality_df[quality_df['smiles'] == target_molecule]
        if len(target_in_report) > 0:
            selected_row = target_in_report.iloc[0]['selected_row_index']
            status = target_in_report.iloc[0]['status']
            print(f"   质量报告: {status}, 选择行{selected_row}")
        else:
            print(f"   质量报告: 未找到该分子")
    
    # 检查对应的图像文件
    target_images = [f for f in png_files if target_molecule in f.name]
    print(f"   生成的图像文件数: {len(target_images)}")
    
    print(f"\n✅ 优化验证完成!")
    
    if len(duplicated_images) == 0 and quality_report_file.exists():
        print(f"🎉 优化成功：每个分子只生成一个高质量的光谱图！")
    else:
        print(f"⚠️  仍需进一步优化")

if __name__ == '__main__':
    verify_optimization()
