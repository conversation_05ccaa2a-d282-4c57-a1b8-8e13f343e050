#!/usr/bin/env python3

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def debug_specific_molecule():
    """调试特定分子的绘图问题"""
    
    # 读取预测数据
    df = pd.read_csv('trained_ir_model/experiment_model/preds_smoke.csv')
    
    # 找到特定分子
    target_smiles = 'CC(C)(C)C1=CC(=O)C=C(C(C)(C)C)C1=O'
    row = df[df['smiles'] == target_smiles]
    
    if row.empty:
        print('未找到该分子')
        return
    
    # 获取频率列（除了smiles列）
    freq_cols = [col for col in df.columns if col != 'smiles']
    
    # 获取预测值并转换为数值
    y_vals = pd.to_numeric(row.iloc[0][freq_cols], errors='coerce').values
    
    # 生成频率轴（从400到4000 cm^-1，间隔2 cm^-1）
    freqs = np.arange(400, 4002, 2)
    
    print(f'分子: {target_smiles}')
    print(f'频率点数: {len(freqs)}')
    print(f'预测值点数: {len(y_vals)}')
    print(f'频率范围: {freqs[0]} - {freqs[-1]} cm^-1')
    print(f'预测值范围: {np.min(y_vals):.6f} - {np.max(y_vals):.6f}')
    
    # 检查数据长度是否匹配
    if len(freqs) != len(y_vals):
        print(f'警告：频率点数({len(freqs)})与预测值点数({len(y_vals)})不匹配！')
        min_len = min(len(freqs), len(y_vals))
        freqs = freqs[:min_len]
        y_vals = y_vals[:min_len]
        print(f'截取到相同长度: {min_len}')
    
    # 检查是否有NaN值
    nan_count = np.isnan(y_vals).sum()
    if nan_count > 0:
        print(f'发现 {nan_count} 个NaN值')
        nan_indices = np.where(np.isnan(y_vals))[0]
        print(f'NaN位置: {nan_indices[:10]}...' if len(nan_indices) > 10 else f'NaN位置: {nan_indices}')
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 绘制光谱
    ax.plot(freqs, y_vals, lw=1.2, color='blue', marker='', linestyle='-')
    ax.set_xlabel("Wavenumber (cm^-1)")
    ax.set_ylabel("Predicted intensity")
    ax.invert_xaxis()  # IR光谱通常是高频到低频
    ax.set_title(f'IR Spectrum Debug: {target_smiles}')
    ax.grid(True, ls=':', alpha=0.4)
    
    # 添加一些调试信息
    ax.text(0.02, 0.98, f'Data points: {len(y_vals)}\nNaN count: {nan_count}', 
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图像
    output_file = 'debug_spectrum.png'
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    print(f'调试图像已保存为: {output_file}')
    
    # 显示一些数据样本
    print('\n数据样本:')
    for i in range(0, len(freqs), len(freqs)//10):
        print(f'  频率 {freqs[i]:6.1f} cm^-1: 强度 {y_vals[i]:.6f}')
    
    plt.close()

if __name__ == '__main__':
    debug_specific_molecule()
